from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
import random
import string

class User(AbstractUser):
    USER_TYPE_CHOICES = (
        ('student', 'طالب'),
        ('lecturer', 'محاضر'),
        ('admin', 'مدير'),
    )
    
    email = models.EmailField('البريد الإلكتروني', unique=True)
    full_name = models.Char<PERSON>ield('الاسم الكامل', max_length=255)
    user_type = models.Cha<PERSON><PERSON><PERSON>('نوع المستخدم', max_length=20, choices=USER_TYPE_CHOICES)
    student_id = models.Char<PERSON><PERSON>('الرقم الدراسي', max_length=20, blank=True, null=True)
    is_active = models.BooleanField('نشط', default=True)
    is_approved = models.BooleanField('تمت الموافقة', default=False)
    password_changed = models.<PERSON><PERSON>anField('تم تغيير كلمة المرور', default=False)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'full_name']
    
    def __str__(self):
        return self.email

class OTPCode(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='otp_codes')
    code = models.CharField('رمز التحقق', max_length=6)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    expires_at = models.DateTimeField('تاريخ الانتهاء')
    
    def __str__(self):
        return f"{self.user.email} - {self.code}"
    
    def is_valid(self):
        return timezone.now() < self.expires_at
    
    @classmethod
    def generate_otp(cls, user):
        # Generate a 6-digit OTP code
        code = ''.join(random.choices(string.digits, k=6))
        # Set expiration time (10 minutes from now)
        expires_at = timezone.now() + timezone.timedelta(minutes=10)
        # Create and return the OTP object
        return cls.objects.create(user=user, code=code, expires_at=expires_at)
