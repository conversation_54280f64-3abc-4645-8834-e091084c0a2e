{% extends 'admin/base_site.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extrahead %}
<style>
    .update-image-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .update-image-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .update-image-header {
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .update-image-header h1 {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .update-image-header p {
        opacity: 0.9;
        margin: 0;
    }

    .update-image-body {
        padding: 2rem;
    }

    .current-image-section {
        background: #f7fafc;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .current-image-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 1rem;
    }

    .current-image-display {
        display: inline-block;
        position: relative;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    .current-image-display img {
        width: 200px;
        height: 150px;
        object-fit: cover;
        display: block;
    }

    .no-image-placeholder {
        width: 200px;
        height: 150px;
        background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: #718096;
        border-radius: 15px;
    }

    .no-image-placeholder i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .upload-section {
        background: white;
        border: 2px dashed #cbd5e0;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .upload-section:hover {
        border-color: #5260bf;
        background: #f7fafc;
    }

    .upload-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #5260bf, #667eea);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 2rem;
    }

    .upload-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .upload-subtitle {
        color: #718096;
        margin-bottom: 1.5rem;
    }

    .file-input-wrapper {
        position: relative;
        display: inline-block;
    }

    .file-input-wrapper input[type="file"] {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .file-input-btn {
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .file-input-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(82, 96, 191, 0.3);
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e2e8f0;
    }

    .btn-modern {
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        border: none;
        cursor: pointer;
    }

    .btn-primary {
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(82, 96, 191, 0.3);
        color: white;
    }

    .btn-secondary {
        background: #e2e8f0;
        color: #4a5568;
    }

    .btn-secondary:hover {
        background: #cbd5e0;
        color: #2d3748;
    }

    .btn-danger {
        background: linear-gradient(135deg, #e53e3e, #c53030);
        color: white;
    }

    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(229, 62, 62, 0.3);
        color: white;
    }

    .image-preview {
        margin-top: 1rem;
        text-align: center;
    }

    .image-preview img {
        max-width: 200px;
        max-height: 150px;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .error-message {
        background: #fed7d7;
        color: #c53030;
        padding: 1rem;
        border-radius: 12px;
        margin-bottom: 1rem;
        text-align: center;
    }

    .success-message {
        background: #c6f6d5;
        color: #2f855a;
        padding: 1rem;
        border-radius: 12px;
        margin-bottom: 1rem;
        text-align: center;
    }

    @media (max-width: 768px) {
        .update-image-container {
            margin: 1rem auto;
            padding: 0 0.5rem;
        }

        .update-image-body {
            padding: 1.5rem;
        }

        .current-image-display img,
        .no-image-placeholder {
            width: 150px;
            height: 112px;
        }

        .form-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn-modern {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="update-image-container">
    <div class="update-image-card">
        <div class="update-image-header">
            <h1>{{ page_title }}</h1>
            <p>{{ course.name }} ({{ course.code }})</p>
        </div>

        <div class="update-image-body">
            <!-- عرض الرسائل -->
            {% if messages %}
            {% for message in messages %}
            <div class="{% if message.tags == 'error' %}error-message{% else %}success-message{% endif %}">
                {{ message }}
            </div>
            {% endfor %}
            {% endif %}

            <!-- الصورة الحالية -->
            <div class="current-image-section">
                <h3 class="current-image-title">الصورة الحالية</h3>
                {% if course.image %}
                <div class="current-image-display">
                    <img src="{{ course.image.url }}" alt="{{ course.name }}">
                </div>
                {% else %}
                <div class="no-image-placeholder">
                    <i class="fas fa-image"></i>
                    <span>لا توجد صورة</span>
                </div>
                {% endif %}
            </div>

            <!-- نموذج رفع الصورة -->
            <form method="post" enctype="multipart/form-data" id="imageForm">
                {% csrf_token %}
                
                <div class="upload-section" id="uploadSection">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <h3 class="upload-title">رفع صورة جديدة</h3>
                    <p class="upload-subtitle">اختر صورة جديدة لاستبدال الصورة الحالية</p>
                    
                    <div class="file-input-wrapper">
                        {{ form.image }}
                        <div class="file-input-btn">
                            <i class="fas fa-upload"></i>
                            اختيار صورة
                        </div>
                    </div>
                    
                    <div class="image-preview" id="imagePreview"></div>
                </div>

                {% if form.image.errors %}
                <div class="error-message">
                    {% for error in form.image.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}

                <div class="form-actions">
                    <button type="submit" class="btn-modern btn-primary">
                        <i class="fas fa-check-circle"></i>
                        حفظ الصورة
                    </button>
                    
                    {% if course.image %}
                    <button type="button" class="btn-modern btn-danger" onclick="deleteImage()">
                        <i class="fas fa-trash"></i>
                        حذف الصورة
                    </button>
                    {% endif %}
                    
                    <a href="{% url 'admin:core_course_changelist' %}" class="btn-modern btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.querySelector('input[type="file"]');
    const uploadSection = document.getElementById('uploadSection');
    const imagePreview = document.getElementById('imagePreview');

    // معاينة الصورة عند الاختيار
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = `
                    <img src="${e.target.result}" alt="معاينة الصورة">
                    <p style="margin-top: 0.5rem; color: #28a745; font-weight: 500;">
                        <i class="fas fa-check-circle"></i>
                        تم اختيار الصورة: ${file.name}
                    </p>
                `;
            };
            reader.readAsDataURL(file);
        }
    });

    // Drag and drop functionality
    uploadSection.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadSection.style.borderColor = '#5260bf';
        uploadSection.style.background = '#edf2f7';
    });

    uploadSection.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadSection.style.borderColor = '#cbd5e0';
        uploadSection.style.background = 'white';
    });

    uploadSection.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadSection.style.borderColor = '#cbd5e0';
        uploadSection.style.background = 'white';

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            imageInput.files = files;
            imageInput.dispatchEvent(new Event('change'));
        }
    });
});

function deleteImage() {
    if (confirm('هل أنت متأكد من حذف الصورة؟')) {
        fetch(`{% url 'core:delete_course_image' course.id %}`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
{% endblock %}
