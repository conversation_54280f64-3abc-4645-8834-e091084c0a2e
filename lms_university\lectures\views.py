from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from .models import Lecture
from .forms import LectureForm, LectureImageForm
import os
from core.models import Course
from accounts.models import User

# Views للطلاب والعامة
@login_required
def lecture_list(request):
    """قائمة المحاضرات للطلاب"""
    if request.user.user_type == 'student':
        # عرض المحاضرات التي يمكن للطالب الوصول إليها
        from core.models import CourseEnrollment

        # الحصول على المقررات المسجل فيها الطالب
        enrolled_courses = CourseEnrollment.objects.filter(
            student=request.user,
            status='enrolled'
        ).values_list('course_id', flat=True)

        # الحصول على المحاضرات من هذه المقررات
        lectures = Lecture.objects.filter(
            course_id__in=enrolled_courses
        ).select_related('course', 'lecturer').order_by('-date')

        # فلترة المحاضرات حسب صلاحيات الوصول
        accessible_lectures = []
        for lecture in lectures:
            if lecture.can_student_access(request.user):
                accessible_lectures.append(lecture)

        lectures = accessible_lectures
    else:
        # للمدرسين والمديرين، عرض جميع المحاضرات
        lectures = Lecture.objects.all().select_related('course', 'lecturer').order_by('-date')

    return render(request, 'lectures/lecture_list.html', {'lectures': lectures})

@login_required
def lecture_detail(request, pk):
    """تفاصيل المحاضرة مع فحص الصلاحيات"""
    lecture = get_object_or_404(Lecture, pk=pk)

    # فحص صلاحية الوصول للطلاب
    if request.user.user_type == 'student':
        if not lecture.can_student_access(request.user):
            messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه المحاضرة.')
            return redirect('lectures:lecture_list')

    # للمدرسين، التحقق من ملكية المحاضرة أو السماح للمديرين
    elif request.user.user_type == 'lecturer':
        if lecture.lecturer != request.user and request.user.user_type != 'admin':
            messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه المحاضرة.')
            return redirect('lectures:lecture_list')

    return render(request, 'lectures/lecture_detail.html', {'lecture': lecture})

# Views للمدرسين
@login_required
def lecturer_dashboard(request):
    """لوحة تحكم المدرس"""
    if request.user.user_type != 'lecturer':
        messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه الصفحة.')
        return redirect('core:dashboard')

    # إحصائيات المدرس
    # مؤقتاً: الحصول على المقررات من خلال المحاضرات الموجودة
    lecturer_course_ids = Lecture.objects.filter(lecturer=request.user).values_list('course_id', flat=True).distinct()
    assigned_courses = Course.objects.filter(id__in=lecturer_course_ids, is_active=True)
    total_assigned_courses = assigned_courses.count()

    # الحصول على المحاضرات
    total_lectures = Lecture.objects.filter(lecturer=request.user).count()

    # أحدث المحاضرات
    recent_lectures = Lecture.objects.filter(
        lecturer=request.user
    ).select_related('course').order_by('-date')[:5]

    context = {
        'total_courses': total_assigned_courses,
        'total_lectures': total_lectures,
        'recent_lectures': recent_lectures,
        'assigned_courses': assigned_courses,
    }

    return render(request, 'lectures/lecturer_dashboard.html', context)

@login_required
def lecturer_lectures(request):
    """قائمة محاضرات المدرس"""
    if request.user.user_type != 'lecturer':
        messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه الصفحة.')
        return redirect('core:dashboard')

    # مؤقتاً: فلترة المحاضرات للمحاضر
    lectures = Lecture.objects.filter(lecturer=request.user).order_by('-date')

    # الحصول على المقررات من خلال المحاضرات
    lecturer_course_ids = lectures.values_list('course_id', flat=True).distinct()
    assigned_courses = Course.objects.filter(id__in=lecturer_course_ids, is_active=True)

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        lectures = lectures.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(course__name__icontains=search_query)
        )

    # فلترة حسب المقرر
    course_filter = request.GET.get('course', '')
    if course_filter:
        lectures = lectures.filter(course_id=course_filter)

    # التصفح
    paginator = Paginator(lectures, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'course_filter': course_filter,
        'assigned_courses': assigned_courses,
    }

    return render(request, 'lectures/lecturer_lectures.html', context)

@login_required
@require_POST
def delete_lecture_image(request, lecture_id):
    """حذف صورة المحاضرة"""
    lecture = get_object_or_404(Lecture, id=lecture_id)

    # التحقق من الصلاحيات
    if not (request.user.is_staff or lecture.lecturer == request.user):
        return JsonResponse({'success': False, 'error': 'ليس لديك صلاحية لحذف هذه الصورة'})

    if lecture.image:
        # حذف الملف من النظام
        if os.path.isfile(lecture.image.path):
            os.remove(lecture.image.path)

        # حذف المرجع من قاعدة البيانات
        lecture.image.delete()
        lecture.save()

        return JsonResponse({'success': True, 'message': 'تم حذف الصورة بنجاح'})
    else:
        return JsonResponse({'success': False, 'error': 'لا توجد صورة لحذفها'})

@login_required
def update_lecture_image(request, lecture_id):
    """تحديث صورة المحاضرة"""
    lecture = get_object_or_404(Lecture, id=lecture_id)

    # التحقق من الصلاحيات
    if not (request.user.is_staff or lecture.lecturer == request.user):
        messages.error(request, 'ليس لديك صلاحية لتعديل هذه المحاضرة.')
        return redirect('lectures:lecture_list')

    if request.method == 'POST':
        form = LectureImageForm(request.POST, request.FILES, instance=lecture)
        if form.is_valid():
            # حذف الصورة القديمة إذا كانت موجودة
            if lecture.image and form.cleaned_data.get('image'):
                if os.path.isfile(lecture.image.path):
                    os.remove(lecture.image.path)

            form.save()
            messages.success(request, 'تم تحديث صورة المحاضرة بنجاح.')
            return redirect('lectures:lecturer_lectures')
        else:
            messages.error(request, 'حدث خطأ في تحديث الصورة. يرجى المحاولة مرة أخرى.')
    else:
        form = LectureImageForm(instance=lecture)

    context = {
        'form': form,
        'lecture': lecture,
        'page_title': 'تحديث صورة المحاضرة'
    }

    return render(request, 'lectures/update_image.html', context)

@login_required
def add_lecture(request):
    """إضافة محاضرة جديدة"""
    if request.user.user_type != 'lecturer':
        messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه الصفحة.')
        return redirect('core:dashboard')

    # مؤقتاً: السماح بإضافة محاضرات لجميع المقررات
    from core.models import Course
    assigned_courses = Course.objects.filter(is_active=True)

    if request.method == 'POST':
        form = LectureForm(request.POST, request.FILES, lecturer=request.user)
        if form.is_valid():
            lecture = form.save(commit=False)
            lecture.lecturer = request.user
            lecture.save()
            messages.success(request, f'تم إضافة المحاضرة "{lecture.title}" بنجاح!')
            return redirect('lectures:lecturer_lectures')
    else:
        form = LectureForm(lecturer=request.user)

    context = {
        'form': form,
        'page_title': 'إضافة محاضرة جديدة',
        'assigned_courses': assigned_courses,
    }

    return render(request, 'lectures/add_lecture.html', context)

@login_required
def edit_lecture(request, lecture_id):
    """تعديل محاضرة"""
    lecture = get_object_or_404(Lecture, id=lecture_id, lecturer=request.user)

    if request.method == 'POST':
        form = LectureForm(request.POST, request.FILES, instance=lecture, lecturer=request.user)
        if form.is_valid():
            lecture = form.save()
            messages.success(request, f'تم تحديث المحاضرة "{lecture.title}" بنجاح!')
            return redirect('lectures:lecturer_lectures')
    else:
        form = LectureForm(instance=lecture, lecturer=request.user)

    context = {
        'form': form,
        'lecture': lecture,
        'page_title': f'تعديل المحاضرة: {lecture.title}'
    }

    return render(request, 'lectures/edit_lecture.html', context)

@login_required
def delete_lecture(request, lecture_id):
    """حذف محاضرة"""
    lecture = get_object_or_404(Lecture, id=lecture_id, lecturer=request.user)

    if request.method == 'POST':
        lecture_title = lecture.title
        lecture.delete()
        messages.success(request, f'تم حذف المحاضرة "{lecture_title}" بنجاح!')
        return redirect('lectures:lecturer_lectures')

    context = {
        'lecture': lecture,
    }

    return render(request, 'lectures/delete_lecture.html', context)

@login_required
@require_POST
def duplicate_lecture(request, lecture_id):
    """تكرار محاضرة"""
    lecture = get_object_or_404(Lecture, id=lecture_id, lecturer=request.user)

    # إنشاء نسخة من المحاضرة
    new_lecture = Lecture.objects.create(
        course=lecture.course,
        title=f"نسخة من {lecture.title}",
        description=lecture.description,
        lecturer=lecture.lecturer,
        # لا ننسخ الملف لتجنب التضارب
    )

    messages.success(request, f'تم تكرار المحاضرة بنجاح! يمكنك الآن تعديلها.')
    return redirect('lectures:edit_lecture', lecture_id=new_lecture.id)
