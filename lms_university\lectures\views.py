from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from .models import Lecture

@login_required
def lecture_list(request):
    lectures = Lecture.objects.all()
    return render(request, 'lectures/lecture_list.html', {'lectures': lectures})

@login_required
def lecture_detail(request, pk):
    lecture = get_object_or_404(Lecture, pk=pk)
    return render(request, 'lectures/lecture_detail.html', {'lecture': lecture})
