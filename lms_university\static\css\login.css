/*
 * ملف CSS خاص بصفحة تسجيل الدخول
 * Login Page Styles - External CSS File
 * تم تحديثه ليعمل كملف خارجي مع أولوية عالية
 * Updated to work as external file with high priority
 */

/* الخلفية الأساسية */
body {
    background: linear-gradient(135deg, #5260bf 0%, #764ba2 100%) !important;
    min-height: 100vh !important;
    font-family: 'Cairo', sans-serif !important;
    font-weight: 400;
}

/* Global Cairo Font Application */
* {
    font-family: 'Cairo', sans-serif !important;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600;
}

.btn {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500;
}

.form-control, .form-label {
    font-family: 'Cairo', sans-serif !important;
}

.card-title {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 700;
}

.alert {
    font-family: 'Cairo', sans-serif !important;
}

/* حاوي صفحة تسجيل الدخول */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* بطاقة تسجيل الدخول */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 450px;
    width: 100%;
    animation: slideUp 0.6s ease-out;
}

/* تأثير الانزلاق للأعلى */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* رأس بطاقة تسجيل الدخول */
.login-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

/* نمط الخلفية للرأس */
.login-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

/* أيقونة تسجيل الدخول */
.login-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
}

/* عنوان تسجيل الدخول */
.login-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    position: relative;
    z-index: 1;
}

/* العنوان الفرعي */
.login-subtitle {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0.5rem 0 0 0;
    position: relative;
    z-index: 1;
}

/* جسم بطاقة تسجيل الدخول */
.login-body {
    padding: 2.5rem;
}

/* حقول النموذج العائمة */
.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating > .form-control {
    border: 2px solid #e9ecef !important;
    border-radius: 12px !important;
    padding: 1rem 0.75rem !important;
    height: auto !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: rgba(255, 255, 255, 0.8) !important;
    /* محاذاة النص إلى اليمين */
    text-align: right !important;
    direction: rtl !important;
}

.form-floating > .form-control:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
    background: white !important;
    transform: translateY(-2px) !important;
}

.form-floating > label {
    color: #6c757d !important;
    font-weight: 500 !important;
    /* محاذاة التسمية مع اتجاه النص */
    text-align: right !important;
    direction: rtl !important;
}

/* زر تسجيل الدخول */
.btn-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 0.875rem 2rem !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: white !important;
    width: 100% !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

/* تأثير الإضاءة للزر */
.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-login:hover::before {
    left: 100%;
}

.btn-login:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3) !important;
}

.btn-login:active {
    transform: translateY(0) !important;
}

/* رسائل الخطأ */
.error-message {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52) !important;
    color: white !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 1rem !important;
    margin-bottom: 1.5rem !important;
    font-size: 0.9rem !important;
    animation: shake 0.5s ease-in-out !important;
}

/* تأثير الاهتزاز للأخطاء */
@keyframes shake {
    0%, 20%, 40%, 60%, 80% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
}

/* أخطاء الحقول */
.field-error {
    color: #dc3545 !important;
    font-size: 0.875rem !important;
    margin-top: 0.5rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
}

/* نص الترحيب */
.welcome-text {
    text-align: center !important;
    margin-bottom: 2rem !important;
    color: #6c757d !important;
}

.welcome-text h4 {
    color: #495057 !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
}

/* ملاحظة الأمان */
.security-note {
    background: rgba(102, 126, 234, 0.1) !important;
    border: 1px solid rgba(102, 126, 234, 0.2) !important;
    border-radius: 12px !important;
    padding: 1rem !important;
    margin-top: 1.5rem !important;
    text-align: center !important;
    font-size: 0.875rem !important;
    color: #667eea !important;
}

/* مؤشر التحميل */
.loading-spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

/* تأثير الدوران */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تأثير التموج للزر */
.btn-login {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* تأثير التركيز للحقول */
.form-floating.focused > .form-control {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تنسيق خاص لحقول البريد الإلكتروني وكلمة السر */
input[type="email"],
input[type="password"],
input[name="username"] {
    text-align: right !important;
    direction: rtl !important;
    padding-right: 1rem !important;
    padding-left: 1rem !important;
}

/* تنسيق التسميات لحقول البريد الإلكتروني وكلمة السر */
.form-floating > label[for*="username"],
.form-floating > label[for*="password"] {
    text-align: right !important;
    direction: rtl !important;
    right: 1rem !important;
    left: auto !important;
}

/* التصميم المتجاوب */
@media (max-width: 576px) {
    .login-container {
        padding: 10px;
    }

    .login-header {
        padding: 1.5rem;
    }

    .login-body {
        padding: 1.5rem;
    }

    .login-icon {
        font-size: 2.5rem;
    }

    .login-title {
        font-size: 1.25rem;
    }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .login-card {
        background: rgba(33, 37, 41, 0.95);
        color: #f8f9fa;
    }

    .form-floating > .form-control {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: #495057 !important;
        color: #f8f9fa !important;
        /* محاذاة النص إلى اليمين في الوضع المظلم */
        text-align: right !important;
        direction: rtl !important;
    }

    .form-floating > .form-control:focus {
        background: rgba(255, 255, 255, 0.15) !important;
    }

    .form-floating > label {
        color: #adb5bd !important;
        /* محاذاة التسمية مع اتجاه النص في الوضع المظلم */
        text-align: right !important;
        direction: rtl !important;
    }
}
