{% extends 'base.html' %}
{% load static %}
{% block title %}الملف الشخصي{% endblock %}

{% block extra_css %}
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{% static 'css/profile_modern.css' %}">
{% endblock %}
{% block content %}
<div class="profile-container">
    <!-- Profile Header -->
    <div class="profile-header">
        <a href="{% url 'core:dashboard' %}" class="back-button">
            <i class="bi bi-arrow-right"></i>
            العودة للوحة التحكم
        </a>

        <div class="profile-avatar-section">
            {% if user.photo %}
                <img src="{{ user.photo.url }}" alt="الصورة الشخصية" class="profile-avatar">
            {% else %}
                <div class="avatar-placeholder">
                    <i class="bi bi-person-fill"></i>
                </div>
            {% endif %}
        </div>

        <div class="profile-info">
            <h1>{{ user.full_name }}</h1>
            <p class="profile-email">{{ user.email }}</p>

            <!-- User ID based on user type -->
            <div class="profile-id">
                {% if user.user_type == 'student' %}
                    <span class="id-label">الرقم الدراسي:</span>
                    <span class="id-number">{{ user.student_id|default_if_none:'غير محدد' }}</span>
                {% elif user.user_type == 'lecturer' %}
                    <span class="id-label">الرقم الأكاديمي:</span>
                    <span class="id-number">{{ user.employee_id|default_if_none:'غير محدد' }}</span>
                {% elif user.user_type == 'admin' %}
                    <span class="id-label">الرقم الأكاديمي:</span>
                    <span class="id-number">{{ user.employee_id|default_if_none:'غير محدد' }}</span>
                {% else %}
                    <span class="id-label">الرقم الوظيفي:</span>
                    <span class="id-number">{{ user.employee_id|default_if_none:'غير محدد' }}</span>
                {% endif %}
            </div>

            <span class="profile-badge">
                {% if user.user_type == 'lecturer' %}
                    محاضر
                {% elif user.user_type == 'student' %}
                    طالب
                {% elif user.user_type == 'admin' %}
                    مدير النظام
                {% else %}
                    مستخدم
                {% endif %}
            </span>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
    <div class="alert-modern {% if 'error' in messages.tags %}alert-error{% endif %}">
        <i class="bi bi-check-circle"></i>
        {% for message in messages %}
            {{ message }}
        {% endfor %}
    </div>
    {% endif %}

    <!-- Profile Content Wrapper -->
    <div class="profile-content-wrapper">
        <!-- Left Section: Personal Information -->
        <div class="profile-left-section">
            <!-- Profile Form -->
            <div class="profile-form-card">
                <h2 class="form-section-title">
                    <i class="bi bi-person-lines-fill"></i>
                    تحديث البيانات الشخصية
                </h2>

                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}

            <div class="form-row">
                <!-- Personal Information -->
                <div>
                    <div class="form-group-modern">
                        <label class="form-label-modern">الاسم الكامل</label>
                        <input type="text" class="form-control-modern" value="{{ user.full_name|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>

                    <div class="form-group-modern">
                        <label class="form-label-modern">رقم الهاتف</label>
                        <input name="phone" class="form-control-modern {% if form.phone.errors %}form-control-error{% endif %}"
                               value="{{ form.phone.value|default_if_none:'' }}" placeholder="أدخل رقم الهاتف">
                        {% if form.phone.errors %}
                            {% for error in form.phone.errors %}
                            <div class="error-text">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <div class="form-group-modern">
                        <label class="form-label-modern">البريد الإلكتروني</label>
                        <input name="email" class="form-control-modern {% if form.email.errors %}form-control-error{% endif %}"
                               value="{{ form.email.value|default_if_none:user.email }}" placeholder="أدخل البريد الإلكتروني">
                        {% if form.email.errors %}
                            {% for error in form.email.errors %}
                            <div class="error-text">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>

                <!-- Photo Upload -->
                <div>
                    <div class="form-group-modern">
                        <label class="form-label-modern">الصورة الشخصية</label>
                        <div class="file-upload-modern">
                            <div class="upload-icon">
                                <i class="bi bi-camera"></i>
                            </div>
                            <div class="upload-text">اختر صورة شخصية</div>
                            <div class="upload-hint">PNG, JPG, GIF حتى 2MB</div>
                            {{ form.photo }}
                        </div>
                        {% if form.photo.errors %}
                            {% for error in form.photo.errors %}
                            <div class="error-text">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Additional Fields -->
            <div class="form-group-modern">
                <label class="form-label-modern">العنوان</label>
                <input name="address" class="form-control-modern {% if form.address.errors %}form-control-error{% endif %}"
                       value="{{ form.address.value|default_if_none:'' }}" placeholder="أدخل العنوان">
                {% if form.address.errors %}
                    {% for error in form.address.errors %}
                    <div class="error-text">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>

            <div class="form-group-modern">
                <label class="form-label-modern">نبذة تعريفية</label>
                <textarea name="bio" class="form-control-modern {% if form.bio.errors %}form-control-error{% endif %}"
                          rows="4" placeholder="اكتب نبذة تعريفية عنك">{{ form.bio.value|default_if_none:'' }}</textarea>
                {% if form.bio.errors %}
                    {% for error in form.bio.errors %}
                    <div class="error-text">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
            </div>
        </div>

        <!-- Right Section: Academic Information -->
        <div class="profile-right-section">
            <div class="profile-form-card">
                <!-- Academic Information -->
            {% if user.user_type == 'student' %}
            <div style="margin-top: 1rem;">
                <h3 class="form-section-title">
                    <i class="bi bi-mortarboard-fill"></i>
                    المعلومات الأكاديمية
                </h3>

                <div class="form-row">
                    <div class="form-group-modern">
                        <label class="form-label-modern">الرقم الدراسي</label>
                        <input type="text" class="form-control-modern" value="{{ user.student_id|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>

                    <div class="form-group-modern">
                        <label class="form-label-modern">الكلية</label>
                        <input type="text" class="form-control-modern" value="{{ user.faculty.name|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group-modern">
                        <label class="form-label-modern">القسم</label>
                        <input type="text" class="form-control-modern" value="{{ user.department.name|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>

                    <div class="form-group-modern">
                        <label class="form-label-modern">التخصص</label>
                        <input type="text" class="form-control-modern" value="{{ user.specialization|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group-modern">
                        <label class="form-label-modern">سنة الالتحاق</label>
                        <input type="text" class="form-control-modern" value="{{ user.enrollment_year|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>

                    <div class="form-group-modern">
                        <label class="form-label-modern">المستوى الدراسي</label>
                        <input type="text" class="form-control-modern" value="{{ user.level|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>
                </div>

                <div class="form-group-modern">
                    <label class="form-label-modern">المعدل التراكمي</label>
                    <input type="text" class="form-control-modern" value="{{ user.gpa|default_if_none:'لا يوجد' }}" readonly disabled>
                </div>
            </div>
            {% elif user.user_type == 'lecturer' %}
            <div style="margin-top: 1rem;">
                <h3 class="form-section-title">
                    <i class="bi bi-mortarboard-fill"></i>
                    المعلومات الأكاديمية
                </h3>

                <div class="form-row">
                    <div class="form-group-modern">
                        <label class="form-label-modern">الدرجة العلمية</label>
                        <input type="text" class="form-control-modern" value="{{ user.academic_degree|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>

                    <div class="form-group-modern">
                        <label class="form-label-modern">التخصص</label>
                        <input type="text" class="form-control-modern" value="{{ user.specialization|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group-modern">
                        <label class="form-label-modern">المكتب</label>
                        <input type="text" class="form-control-modern" value="{{ user.office|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>

                    <div class="form-group-modern">
                        <label class="form-label-modern">تاريخ التعيين</label>
                        <input type="text" class="form-control-modern" value="{{ user.hire_date|default_if_none:'لا يوجد' }}" readonly disabled>
                    </div>
                </div>
            </div>
            {% endif %}
                    <!-- Save Button -->
                    <div style="text-align: center; margin-top: 1rem;">
                        <button type="submit" class="btn-save-modern">
                            <i class="bi bi-check-circle"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>

    <!-- Password Change Section -->
    <div class="password-section">
        <div class="password-icon">
            <i class="bi bi-shield-lock"></i>
        </div>
        <h2 class="password-title">تغيير كلمة المرور</h2>
        <p class="password-description">
            لحماية حسابك، يمكنك تغيير كلمة المرور في أي وقت. تأكد من اختيار كلمة مرور قوية وآمنة.
        </p>
        <a href="{% url 'accounts:password_change' %}" class="btn-password-modern">
            <i class="bi bi-key"></i>
            تغيير كلمة المرور
        </a>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // File upload preview
    const fileInput = document.querySelector('input[type="file"]');
    const uploadArea = document.querySelector('.file-upload-modern');

    if (fileInput && uploadArea) {
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const uploadIcon = uploadArea.querySelector('.upload-icon');
                    const uploadText = uploadArea.querySelector('.upload-text');
                    const uploadHint = uploadArea.querySelector('.upload-hint');

                    uploadIcon.innerHTML = '<i class="bi bi-check-circle"></i>';
                    uploadIcon.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                    uploadText.textContent = 'تم اختيار الصورة';
                    uploadHint.textContent = file.name;
                };
                reader.readAsDataURL(file);
            }
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#5260bf';
            uploadArea.style.background = '#edf2f7';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#cbd5e0';
            uploadArea.style.background = '#f7fafc';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#cbd5e0';
            uploadArea.style.background = '#f7fafc';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                fileInput.dispatchEvent(new Event('change'));
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('.btn-save-modern');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الحفظ...';
                submitBtn.disabled = true;
            }
        });
    }

    // Input animations
    const inputs = document.querySelectorAll('.form-control-modern');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-1px)';
        });

        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %}
