from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Count
from .models import Faculty, Department, Course, CourseEnrollment, LectureAccess
# CourseAssignment مؤقتاً معطل حتى يتم إنشاء الجدول

@admin.register(Faculty)
class FacultyAdmin(admin.ModelAdmin):
    list_display = ('name', 'departments_count', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)

    def departments_count(self, obj):
        return obj.departments.count()
    departments_count.short_description = 'عدد الأقسام'

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'faculty', 'courses_count', 'is_active')
    list_filter = ('faculty', 'is_active')
    search_fields = ('name', 'faculty__name')

    def courses_count(self, obj):
        return obj.courses.count()
    courses_count.short_description = 'عدد المقررات'

@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'department', 'credit_hours', 'image_preview', 'image_actions', 'enrollments_count', 'is_active')
    list_filter = ('department', 'credit_hours', 'is_active')
    search_fields = ('code', 'name', 'department__name')
    fields = ('code', 'name', 'department', 'description', 'credit_hours', 'image', 'is_active')

    def enrollments_count(self, obj):
        return obj.enrollments.filter(status='enrolled').count()
    enrollments_count.short_description = 'عدد الطلاب المسجلين'

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;" />',
                obj.image.url
            )
        return "لا توجد صورة"
    image_preview.short_description = 'صورة المقرر'

    def image_actions(self, obj):
        """أزرار إدارة الصورة"""
        update_url = reverse('core:update_course_image', args=[obj.pk])
        actions = f'<a href="{update_url}" class="button" style="margin: 2px;">إدارة الصورة</a>'

        if obj.image:
            delete_url = reverse('core:delete_course_image', args=[obj.pk])
            actions += f'''
            <button onclick="deleteCourseImage({obj.pk})" class="button" style="margin: 2px; background: #dc3545; color: white;">
                حذف الصورة
            </button>
            <script>
            function deleteCourseImage(courseId) {{
                if (confirm('هل أنت متأكد من حذف الصورة؟')) {{
                    fetch('/core/courses/delete-image/' + courseId + '/', {{
                        method: 'POST',
                        headers: {{
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                            'Content-Type': 'application/json',
                        }},
                    }})
                    .then(response => response.json())
                    .then(data => {{
                        if (data.success) {{
                            location.reload();
                        }} else {{
                            alert('حدث خطأ: ' + data.error);
                        }}
                    }})
                    .catch(error => {{
                        console.error('Error:', error);
                        alert('حدث خطأ في الاتصال');
                    }});
                }}
            }}
            </script>
            '''

        return format_html(actions)
    image_actions.short_description = 'إدارة الصورة'

@admin.register(CourseEnrollment)
class CourseEnrollmentAdmin(admin.ModelAdmin):
    """إدارة تسجيل الطلاب في المقررات"""

    list_display = (
        'student_info',
        'course_info',
        'status_badge',
        'enrollment_date',
        'enrolled_by',
        'actions_column'
    )

    list_filter = (
        'status',
        'enrollment_date',
        'course__department',
        'course__department__faculty',
        'enrolled_by'
    )

    search_fields = (
        'student__full_name',
        'student__email',
        'student__student_id',
        'course__name',
        'course__code'
    )

    ordering = ('-enrollment_date',)

    list_per_page = 25

    fieldsets = (
        ('معلومات التسجيل', {
            'fields': ('student', 'course', 'status')
        }),
        ('تفاصيل إضافية', {
            'fields': ('enrolled_by', 'notes'),
            'classes': ('collapse',)
        }),
    )

    def student_info(self, obj):
        """عرض معلومات الطالب"""
        return format_html(
            '<strong>{}</strong><br><small>{}</small><br><small>ID: {}</small>',
            obj.student.full_name,
            obj.student.email,
            obj.student.student_id or 'غير محدد'
        )
    student_info.short_description = 'الطالب'
    student_info.admin_order_field = 'student__full_name'

    def course_info(self, obj):
        """عرض معلومات المقرر"""
        return format_html(
            '<strong>{}</strong><br><small>{}</small><br><small>{}</small>',
            obj.course.name,
            obj.course.code,
            obj.course.department.name
        )
    course_info.short_description = 'المقرر'
    course_info.admin_order_field = 'course__name'

    def status_badge(self, obj):
        """عرض حالة التسجيل كشارة ملونة"""
        colors = {
            'enrolled': 'success',
            'pending': 'warning',
            'dropped': 'danger',
            'completed': 'info'
        }
        color = colors.get(obj.status, 'secondary')
        return format_html(
            '<span class="badge badge-{}">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = 'الحالة'
    status_badge.admin_order_field = 'status'

    def actions_column(self, obj):
        """عمود الإجراءات"""
        actions = []

        # رابط التعديل
        edit_url = reverse('admin:core_courseenrollment_change', args=[obj.pk])
        actions.append(f'<a href="{edit_url}" class="btn btn-sm btn-primary">تعديل</a>')

        # رابط الحذف
        delete_url = reverse('admin:core_courseenrollment_delete', args=[obj.pk])
        actions.append(f'<a href="{delete_url}" class="btn btn-sm btn-danger">حذف</a>')

        return format_html(' '.join(actions))
    actions_column.short_description = 'الإجراءات'

    def get_queryset(self, request):
        """تحسين الاستعلامات"""
        return super().get_queryset(request).select_related(
            'student',
            'course',
            'course__department',
            'course__department__faculty',
            'enrolled_by'
        )

    def save_model(self, request, obj, form, change):
        """حفظ النموذج مع تسجيل المدير"""
        if not change:  # تسجيل جديد
            obj.enrolled_by = request.user
        super().save_model(request, obj, form, change)

    # الإجراءات المخصصة
    actions = ['approve_enrollments', 'drop_enrollments']

    def approve_enrollments(self, request, queryset):
        """الموافقة على التسجيلات"""
        updated = queryset.update(status='enrolled')
        self.message_user(
            request,
            f'تمت الموافقة على {updated} تسجيل.'
        )
    approve_enrollments.short_description = "الموافقة على التسجيلات المحددة"

    def drop_enrollments(self, request, queryset):
        """إلغاء التسجيلات"""
        updated = queryset.update(status='dropped')
        self.message_user(
            request,
            f'تم إلغاء {updated} تسجيل.'
        )
    drop_enrollments.short_description = "إلغاء التسجيلات المحددة"

@admin.register(LectureAccess)
class LectureAccessAdmin(admin.ModelAdmin):
    """إدارة صلاحيات الوصول للمحاضرات"""

    list_display = (
        'student_info',
        'lecture_info',
        'access_badge',
        'granted_date',
        'expiry_date',
        'granted_by',
        'is_active_status'
    )

    list_filter = (
        'access_type',
        'granted_date',
        'expiry_date',
        'lecture__course',
        'lecture__course__department',
        'granted_by'
    )

    search_fields = (
        'student__full_name',
        'student__email',
        'student__student_id',
        'lecture__title',
        'lecture__course__name',
        'lecture__course__code'
    )

    ordering = ('-granted_date',)

    list_per_page = 25

    fieldsets = (
        ('معلومات الصلاحية', {
            'fields': ('student', 'lecture', 'access_type')
        }),
        ('تفاصيل الصلاحية', {
            'fields': ('granted_by', 'expiry_date', 'notes'),
            'classes': ('collapse',)
        }),
    )

    def student_info(self, obj):
        """عرض معلومات الطالب"""
        return format_html(
            '<strong>{}</strong><br><small>{}</small>',
            obj.student.full_name,
            obj.student.email
        )
    student_info.short_description = 'الطالب'
    student_info.admin_order_field = 'student__full_name'

    def lecture_info(self, obj):
        """عرض معلومات المحاضرة"""
        return format_html(
            '<strong>{}</strong><br><small>{}</small><br><small>{}</small>',
            obj.lecture.title,
            obj.lecture.course.name,
            obj.lecture.course.code
        )
    lecture_info.short_description = 'المحاضرة'
    lecture_info.admin_order_field = 'lecture__title'

    def access_badge(self, obj):
        """عرض نوع الصلاحية كشارة ملونة"""
        colors = {
            'granted': 'success',
            'denied': 'danger',
            'pending': 'warning'
        }
        color = colors.get(obj.access_type, 'secondary')
        return format_html(
            '<span class="badge badge-{}">{}</span>',
            color,
            obj.get_access_type_display()
        )
    access_badge.short_description = 'نوع الصلاحية'
    access_badge.admin_order_field = 'access_type'

    def is_active_status(self, obj):
        """عرض حالة نشاط الصلاحية"""
        if obj.is_active():
            return format_html('<span class="badge badge-success">نشطة</span>')
        else:
            return format_html('<span class="badge badge-secondary">غير نشطة</span>')
    is_active_status.short_description = 'الحالة'

    def get_queryset(self, request):
        """تحسين الاستعلامات"""
        return super().get_queryset(request).select_related(
            'student',
            'lecture',
            'lecture__course',
            'lecture__course__department',
            'granted_by'
        )

    def save_model(self, request, obj, form, change):
        """حفظ النموذج مع تسجيل المانح"""
        if not change:  # صلاحية جديدة
            obj.granted_by = request.user
        super().save_model(request, obj, form, change)

    # الإجراءات المخصصة
    actions = ['grant_access', 'deny_access', 'revoke_access']

    def grant_access(self, request, queryset):
        """منح الصلاحية"""
        updated = queryset.update(access_type='granted')
        self.message_user(
            request,
            f'تم منح الصلاحية لـ {updated} طالب.'
        )
    grant_access.short_description = "منح الصلاحية للمحددين"

    def deny_access(self, request, queryset):
        """منع الصلاحية"""
        updated = queryset.update(access_type='denied')
        self.message_user(
            request,
            f'تم منع الصلاحية عن {updated} طالب.'
        )
    deny_access.short_description = "منع الصلاحية عن المحددين"

    def revoke_access(self, request, queryset):
        """إلغاء الصلاحية"""
        from django.utils import timezone
        updated = queryset.update(
            access_type='denied',
            expiry_date=timezone.now()
        )
        self.message_user(
            request,
            f'تم إلغاء الصلاحية لـ {updated} طالب.'
        )
    revoke_access.short_description = "إلغاء الصلاحية للمحددين"


# مؤقتاً معطل حتى يتم إنشاء الجدول
# @admin.register(CourseAssignment)
class CourseAssignmentAdmin(admin.ModelAdmin):
    """إدارة إسناد المقررات للمحاضرين"""

    list_display = (
        'lecturer_info',
        'course_info',
        'assignment_date',
        'is_active_badge',
        'assignment_period',
        'assigned_by',
        'actions_column'
    )

    list_filter = (
        'is_active',
        'assignment_date',
        'start_date',
        'end_date',
        'course__department',
        'course__department__faculty',
        'assigned_by'
    )

    search_fields = (
        'lecturer__full_name',
        'lecturer__email',
        'course__name',
        'course__code',
        'course__department__name'
    )

    ordering = ('-assignment_date',)

    list_per_page = 25

    fieldsets = (
        ('معلومات الإسناد', {
            'fields': ('lecturer', 'course', 'is_active')
        }),
        ('فترة الإسناد', {
            'fields': ('start_date', 'end_date'),
            'classes': ('collapse',)
        }),
        ('تفاصيل إضافية', {
            'fields': ('assigned_by', 'notes'),
            'classes': ('collapse',)
        }),
    )

    def lecturer_info(self, obj):
        """عرض معلومات المحاضر"""
        return format_html(
            '<strong>{}</strong><br><small>{}</small><br><small>محاضر</small>',
            obj.lecturer.full_name,
            obj.lecturer.email
        )
    lecturer_info.short_description = 'المحاضر'
    lecturer_info.admin_order_field = 'lecturer__full_name'

    def course_info(self, obj):
        """عرض معلومات المقرر"""
        return format_html(
            '<strong>{}</strong><br><small>{}</small><br><small>{}</small>',
            obj.course.name,
            obj.course.code,
            obj.course.department.name
        )
    course_info.short_description = 'المقرر'
    course_info.admin_order_field = 'course__name'

    def is_active_badge(self, obj):
        """عرض حالة الإسناد كشارة ملونة"""
        if obj.is_assignment_active():
            return format_html('<span class="badge badge-success">نشط</span>')
        elif obj.is_active:
            return format_html('<span class="badge badge-warning">معلق</span>')
        else:
            return format_html('<span class="badge badge-danger">غير نشط</span>')
    is_active_badge.short_description = 'الحالة'

    def assignment_period(self, obj):
        """عرض فترة الإسناد"""
        if obj.start_date and obj.end_date:
            return format_html(
                '<small>من: {}<br>إلى: {}</small>',
                obj.start_date.strftime('%Y-%m-%d'),
                obj.end_date.strftime('%Y-%m-%d')
            )
        elif obj.start_date:
            return format_html('<small>من: {}</small>', obj.start_date.strftime('%Y-%m-%d'))
        elif obj.end_date:
            return format_html('<small>إلى: {}</small>', obj.end_date.strftime('%Y-%m-%d'))
        else:
            return format_html('<small>غير محدد</small>')
    assignment_period.short_description = 'فترة الإسناد'

    def actions_column(self, obj):
        """عمود الإجراءات"""
        actions = []

        # رابط التعديل
        edit_url = reverse('admin:core_courseassignment_change', args=[obj.pk])
        actions.append(f'<a href="{edit_url}" class="btn btn-sm btn-primary">تعديل</a>')

        # رابط الحذف
        delete_url = reverse('admin:core_courseassignment_delete', args=[obj.pk])
        actions.append(f'<a href="{delete_url}" class="btn btn-sm btn-danger">حذف</a>')

        return format_html(' '.join(actions))
    actions_column.short_description = 'الإجراءات'

    def get_queryset(self, request):
        """تحسين الاستعلامات"""
        return super().get_queryset(request).select_related(
            'lecturer',
            'course',
            'course__department',
            'course__department__faculty',
            'assigned_by'
        )

    def save_model(self, request, obj, form, change):
        """حفظ النموذج مع تسجيل المدير"""
        if not change:  # إسناد جديد
            obj.assigned_by = request.user
        super().save_model(request, obj, form, change)

    # الإجراءات المخصصة
    actions = ['activate_assignments', 'deactivate_assignments']

    def activate_assignments(self, request, queryset):
        """تفعيل الإسنادات"""
        updated = queryset.update(is_active=True)
        self.message_user(
            request,
            f'تم تفعيل {updated} إسناد.'
        )
    activate_assignments.short_description = "تفعيل الإسنادات المحددة"

    def deactivate_assignments(self, request, queryset):
        """إلغاء تفعيل الإسنادات"""
        updated = queryset.update(is_active=False)
        self.message_user(
            request,
            f'تم إلغاء تفعيل {updated} إسناد.'
        )
    deactivate_assignments.short_description = "إلغاء تفعيل الإسنادات المحددة"
