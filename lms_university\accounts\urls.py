from django.urls import path
from . import views
from django.contrib.auth.views import PasswordChangeView, PasswordChangeDoneView

app_name = 'accounts'

urlpatterns = [
    path('login/', views.login_view, name='login'),
    path('simple-login/', views.simple_login_view, name='simple_login'),  # للاختبار
    path('logout/', views.logout_view, name='logout'),
    path('first-login-change-password/', views.first_login_change_password, name='first_login_change_password'),
    path('verify-otp/', views.verify_otp, name='verify_otp'),
    path('resend-otp/', views.resend_otp, name='resend_otp'),
    path('profile/', views.profile_view, name='profile'),
    path('password_change/', PasswordChangeView.as_view(template_name='accounts/password_change_form.html'), name='password_change'),
    path('password_change/done/', PasswordChangeDoneView.as_view(template_name='accounts/password_change_done.html'), name='password_change_done'),
]