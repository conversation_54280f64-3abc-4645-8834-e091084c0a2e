{% extends 'base.html' %}

{% block title %}تغيير كلمة المرور{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">تغيير كلمة المرور</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <p>مرحباً بك في أول تسجيل دخول. يجب عليك تغيير كلمة المرور المؤقتة.</p>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.old_password.id_for_label }}" class="form-label">{{ form.old_password.label }}</label>
                        {{ form.old_password }}
                        {% if form.old_password.errors %}
                            <div class="text-danger">
                                {% for error in form.old_password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.new_password1.id_for_label }}" class="form-label">{{ form.new_password1.label }}</label>
                        {{ form.new_password1 }}
                        {% if form.new_password1.errors %}
                            <div class="text-danger">
                                {% for error in form.new_password1.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.new_password2.id_for_label }}" class="form-label">{{ form.new_password2.label }}</label>
                        {{ form.new_password2 }}
                        {% if form.new_password2.errors %}
                            <div class="text-danger">
                                {% for error in form.new_password2.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">تغيير كلمة المرور</button>
                    </div>
                </form>
            </div>
        </div>
    </