from django.db import models
from accounts.models import User
from core.models import Course

class Lecture(models.Model):
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='lectures', verbose_name='المقرر')
    title = models.CharField('عنوان المحاضرة', max_length=255)
    description = models.TextField('وصف المحاضرة', blank=True)
    file = models.FileField('ملف المحاضرة', upload_to='lectures/')
    image = models.ImageField('صورة المحاضرة', upload_to='lecture_images/', blank=True, null=True, help_text='صورة غلاف المحاضرة')
    date = models.DateTimeField('تاريخ المحاضرة', auto_now_add=True)
    lecturer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='lectures', verbose_name='المحاضر')

    class Meta:
        verbose_name = 'محاضرة'
        verbose_name_plural = 'المحاضرات'
        ordering = ['-date']

    def __str__(self):
        return f"{self.title} - {self.course.name}"

    def can_lecturer_add_lecture(self, lecturer):
        """التحقق من إمكانية المحاضر إضافة محاضرة لهذا المقرر"""
        from core.models import CourseAssignment

        # التحقق من إسناد المحاضر للمقرر
        try:
            assignment = CourseAssignment.objects.get(
                course=self.course,
                lecturer=lecturer,
                is_active=True
            )
            return assignment.is_assignment_active()
        except CourseAssignment.DoesNotExist:
            return False

    @classmethod
    def get_lecturer_assigned_courses(cls, lecturer):
        """الحصول على المقررات المسندة للمحاضر"""
        from core.models import CourseAssignment

        assignments = CourseAssignment.objects.filter(
            lecturer=lecturer,
            is_active=True
        ).select_related('course')

        return [assignment.course for assignment in assignments if assignment.is_assignment_active()]

    def can_student_access(self, student):
        """تحقق من إمكانية وصول الطالب للمحاضرة"""
        from core.models import CourseEnrollment, LectureAccess

        # التحقق من تسجيل الطالب في المقرر
        try:
            enrollment = CourseEnrollment.objects.get(
                student=student,
                course=self.course,
                status='enrolled'
            )
        except CourseEnrollment.DoesNotExist:
            return False

        # التحقق من صلاحية الوصول للمحاضرة
        try:
            access = LectureAccess.objects.get(
                student=student,
                lecture=self
            )
            return access.is_active()
        except LectureAccess.DoesNotExist:
            # إذا لم توجد صلاحية محددة، السماح بالوصول للطلاب المسجلين
            return True

    def get_enrolled_students(self):
        """الحصول على الطلاب المسجلين في المقرر"""
        from core.models import CourseEnrollment
        return CourseEnrollment.objects.filter(
            course=self.course,
            status='enrolled'
        ).select_related('student')

    def get_students_with_access(self):
        """الحصول على الطلاب الذين لديهم صلاحية وصول"""
        from core.models import LectureAccess
        return LectureAccess.objects.filter(
            lecture=self,
            access_type='granted'
        ).select_related('student')

    def grant_access_to_enrolled_students(self):
        """منح الصلاحية لجميع الطلاب المسجلين في المقرر"""
        from core.models import LectureAccess
        enrolled_students = self.get_enrolled_students()

        for enrollment in enrolled_students:
            LectureAccess.objects.get_or_create(
                student=enrollment.student,
                lecture=self,
                defaults={
                    'access_type': 'granted',
                    'granted_by': self.lecturer
                }
            )
