from django.db import models
from accounts.models import User

class Lecture(models.Model):
    course = models.ForeignKey('courses.Course', on_delete=models.CASCADE, related_name='lectures', verbose_name='المقرر')
    title = models.Char<PERSON>ield('عنوان المحاضرة', max_length=255)
    description = models.TextField('وصف المحاضرة', blank=True)
    file = models.FileField('ملف المحاضرة', upload_to='lectures/')
    date = models.DateTimeField('تاريخ المحاضرة', auto_now_add=True)
    lecturer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='lectures', verbose_name='المحاضر')
    
    class Meta:
        verbose_name = 'محاضرة'
        verbose_name_plural = 'المحاضرات'
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.title} - {self.course.name}"
