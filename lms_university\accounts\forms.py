from django import forms
from django.contrib.auth.forms import AuthenticationForm, PasswordChangeForm
from .models import User, OTPCode

class LoginForm(AuthenticationForm):
    username = forms.EmailField(
        label='البريد الإلكتروني',
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'أدخل البريد الإلكتروني'})
    )
    password = forms.CharField(
        label='كلمة المرور',
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'أدخل كلمة المرور'})
    )

class FirstLoginPasswordChangeForm(PasswordChangeForm):
    old_password = forms.CharField(
        label='كلمة المرور المؤقتة',
        widget=forms.PasswordInput(attrs={'class': 'form-control'})
    )
    new_password1 = forms.CharField(
        label='كلمة المرور الجديدة',
        widget=forms.PasswordInput(attrs={'class': 'form-control'})
    )
    new_password2 = forms.CharField(
        label='تأكيد كلمة المرور الجديدة',
        widget=forms.PasswordInput(attrs={'class': 'form-control'})
    )

class OTPVerificationForm(forms.Form):
    otp_code = forms.CharField(
        label='رمز التحقق',
        max_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'أدخل رمز التحقق المكون من 6 أرقام'
        })
    )
    
    def __init__(self, user=None, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
    
    def clean_otp_code(self):
        code = self.cleaned_data.get('otp_code')
        try:
            otp = OTPCode.objects.filter(user=self.user, code=code).latest('created_at')
            if not otp.is_valid():
                raise forms.ValidationError('رمز التحقق منتهي الصلاحية. الرجاء طلب رمز جديد.')
        except OTPCode.DoesNotExist:
            raise forms.ValidationError('رمز التحقق غير صحيح.')
        return code