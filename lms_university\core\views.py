from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.admin.views.decorators import staff_member_required
from django.db import models
from django.conf import settings
import os
from lectures.models import Lecture
from .models import Course, Faculty, Department
from .forms import CourseForm, CourseImageForm

def home_view(request):
    """Home page view - redirects to login if not authenticated, otherwise to dashboard"""
    if request.user.is_authenticated:
        return redirect('core:dashboard')
    return redirect('accounts:login')

@login_required
def dashboard_view(request):
    """Main dashboard view for authenticated users"""
    # منع الدخول إذا لم يتم التحقق من otp
    if not request.session.get('otp_verified', False):
        return redirect('accounts:verify_otp')

    context = {
        'user': request.user,
        'recent_lectures': Lecture.objects.all()[:5],  # Get 5 most recent lectures
        'total_courses': Course.objects.filter(is_active=True).count(),
        'total_lectures': Lecture.objects.count(),
    }

    # Add user-specific data based on user type
    if request.user.user_type == 'lecturer':
        context['my_lectures'] = Lecture.objects.filter(lecturer=request.user)[:5]
    elif request.user.user_type == 'student':
        # For students, you might want to show enrolled courses
        context['available_courses'] = Course.objects.filter(is_active=True)[:5]

    return render(request, 'core/dashboard.html', context)

@staff_member_required
@require_POST
def delete_course_image(request, course_id):
    """حذف صورة المقرر"""
    course = get_object_or_404(Course, id=course_id)

    if course.image:
        # حذف الملف من النظام
        if os.path.isfile(course.image.path):
            os.remove(course.image.path)

        # حذف المرجع من قاعدة البيانات
        course.image.delete()
        course.save()

        return JsonResponse({'success': True, 'message': 'تم حذف الصورة بنجاح'})
    else:
        return JsonResponse({'success': False, 'error': 'لا توجد صورة لحذفها'})

@staff_member_required
def update_course_image(request, course_id):
    """تحديث صورة المقرر"""
    course = get_object_or_404(Course, id=course_id)

    if request.method == 'POST':
        form = CourseImageForm(request.POST, request.FILES, instance=course)
        if form.is_valid():
            # حذف الصورة القديمة إذا كانت موجودة
            if course.image and form.cleaned_data.get('image'):
                if os.path.isfile(course.image.path):
                    os.remove(course.image.path)

            form.save()
            messages.success(request, 'تم تحديث صورة المقرر بنجاح.')
            return redirect('admin:core_course_changelist')
        else:
            messages.error(request, 'حدث خطأ في تحديث الصورة. يرجى المحاولة مرة أخرى.')
    else:
        form = CourseImageForm(instance=course)

    context = {
        'form': form,
        'course': course,
        'page_title': 'تحديث صورة المقرر'
    }

    return render(request, 'core/update_course_image.html', context)
