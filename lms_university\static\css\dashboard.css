/* dashboard.css */
/* Global Cairo Font Application */
* {
    font-family: 'Cairo', sans-serif !important;
}

body, .card, .form-label, .btn, .alert, .form-control, h1, h2, h3, h4, h5, h6, label, input, select, textarea, p, span, div, a {
    font-family: 'Cairo', sans-serif !important;
}

/* Typography Weights */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
}

.btn {
    font-weight: 500;
}

.card-title {
    font-weight: 600;
}

.dashboard-welcome {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border-radius: 2rem;
    box-shadow: 0 4px 24px 0 rgba(102,126,234,0.13);
    padding: 2.5rem 2rem 2rem 2rem;
    margin-bottom: 2.5rem;
    position: relative;
    overflow: hidden;
    border: 2px solid #fffbe7;
    transition: box-shadow 0.3s, transform 0.3s;
}
.dashboard-welcome:hover {
    box-shadow: 0 8px 32px 0 rgba(102,126,234,0.18);
    transform: translateY(-4px) scale(1.01);
}
.dashboard-welcome .card-title {
    font-size: 2.2rem;
    font-weight: 800;
    letter-spacing: 1px;
    text-shadow: 0 2px 8px rgba(118,75,162,0.08);
}
.dashboard-welcome .card-text {
    font-size: 1.2rem;
    font-weight: 500;
}
.dashboard-welcome .rounded-3 {
    background: linear-gradient(90deg, #fffbe7 0%, #667eea 100%);
    color: #222;
    font-weight: 700;
    font-size: 1.3rem;
    box-shadow: 0 1px 8px 0 rgba(102,126,234,0.05);
}
.stat-card {
    border: none;
    border-radius: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    box-shadow: 0 2px 16px 0 rgba(102,126,234,0.08);
    transition: transform 0.2s;
}
.stat-card:hover {
    transform: translateY(-6px) scale(1.03);
    box-shadow: 0 6px 24px 0 rgba(102,126,234,0.15);
}
.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}
.card-header {
    border-radius: 1.5rem 1.5rem 0 0 !important;
    font-weight: 700;
    font-size: 1.2rem;
}
.list-group-item {
    border-radius: 1rem !important;
    margin-bottom: 0.5rem;
    background: #f5f7fa;
    color: #222;
    transition: box-shadow 0.2s;
}
.list-group-item:hover {
    box-shadow: 0 2px 12px 0 rgba(102,126,234,0.10);
    background: #e9ecef;
}
.badge {
    font-size: 1rem;
    padding: 0.5em 1em;
    border-radius: 1rem;
}
.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
}
.btn-outline-primary:hover {
    background: #667eea;
    color: #fff;
}
.dashboard-welcome img,
.dashboard-welcome i.bi-person-circle {
    border-radius: 50% !important;
    border: 3px solid #fffbe7;
    background: #fffbe7;
    box-shadow: 0 2px 12px 0 rgba(102,126,234,0.10);
}
