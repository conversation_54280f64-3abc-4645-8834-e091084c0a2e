{% extends 'base.html' %}
{% load static %}
{% block title %}تغيير كلمة المرور{% endblock %}
{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/dashboard.css' %}">
{% endblock %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4 rounded-top">
                    <i class="bi bi-shield-lock-fill fs-1 mb-3"></i>
                    <h3 class="card-title mb-0">تغيير كلمة المرور</h3>
                </div>
                <div class="card-body p-4">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-success border-0">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        <!-- كلمة المرور الحالية -->
                        <div class="mb-4">
                            <label for="{{ form.old_password.id_for_label }}" class="form-label fw-bold">
                                <i class="bi bi-lock me-2"></i>{{ form.old_password.label }}
                            </label>
                            <div class="input-group">
                                {{ form.old_password }}
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('{{ form.old_password.id_for_label }}')">
                                    <i class="bi bi-eye" id="toggleIcon1"></i>
                                </button>
                            </div>
                            {% if form.old_password.errors %}
                                {% for error in form.old_password.errors %}
                                    <div class="alert alert-danger mt-2 py-2 px-3">
                                        <i class="bi bi-exclamation-circle me-1"></i> {{ error }}
                                    </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <!-- كلمة المرور الجديدة -->
                        <div class="mb-4">
                            <label for="{{ form.new_password1.id_for_label }}" class="form-label fw-bold">
                                <i class="bi bi-key me-2"></i>{{ form.new_password1.label }}
                            </label>
                            <div class="input-group">
                                {{ form.new_password1 }}
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('{{ form.new_password1.id_for_label }}')">
                                    <i class="bi bi-eye" id="toggleIcon2"></i>
                                </button>
                            </div>
                            {% if form.new_password1.errors %}
                                {% for error in form.new_password1.errors %}
                                    <div class="alert alert-danger mt-2 py-2 px-3">
                                        <i class="bi bi-exclamation-circle me-1"></i> {{ error }}
                                    </div>
                                {% endfor %}
                            {% endif %}
                            <div class="form-text">
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل وتتضمن أرقام وحروف.
                                </small>
                            </div>
                        </div>
                        <!-- تأكيد كلمة المرور الجديدة -->
                        <div class="mb-4">
                            <label for="{{ form.new_password2.id_for_label }}" class="form-label fw-bold">
                                <i class="bi bi-check-circle me-2"></i>{{ form.new_password2.label }}
                            </label>
                            <div class="input-group">
                                {{ form.new_password2 }}
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('{{ form.new_password2.id_for_label }}')">
                                    <i class="bi bi-eye" id="toggleIcon3"></i>
                                </button>
                            </div>
                            {% if form.new_password2.errors %}
                                {% for error in form.new_password2.errors %}
                                    <div class="alert alert-danger mt-2 py-2 px-3">
                                        <i class="bi bi-exclamation-circle me-1"></i> {{ error }}
                                    </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <!-- رسائل الخطأ العامة -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger border-0">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <!-- زر التغيير -->
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-shield-check me-2"></i>
                                تغيير كلمة المرور
                            </button>
                        </div>
                        <!-- ملاحظة أمان -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                <strong>ملاحظة:</strong> بعد تغيير كلمة المرور، ستحتاج لتسجيل الدخول مرة أخرى بكلمة المرور الجديدة.
                            </small>
                        </div>
                    </form>
                    <a href="{% url 'accounts:profile' %}" class="btn btn-link mt-3 w-100">العودة إلى الملف الشخصي</a>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- JavaScript لإظهار/إخفاء كلمة المرور -->
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = field.parentElement.querySelector('i');
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
    }
}
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input[type="password"], input[type="text"]');
    inputs.forEach(input => {
        input.classList.add('form-control');
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('shadow-sm');
        });
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('shadow-sm');
        });
    });
});
</script>
{% endblock %}
