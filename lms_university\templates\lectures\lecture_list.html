{% extends 'base.html' %}
{% load static %}

{% block title %}المحاضرات{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/lecture_list.css' %}">
<link rel="stylesheet" href="{% static 'css/custom_buttons.css' %}">
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-40">
            <div class="col-lg-8">
                <div class="hero-content">
                    <div class="welcome-badge">
                        <i class="bi bi-journal-text align-middle ms-2"></i>
                        مكتبة المحاضرات
                    </div>
                    <h1 class="hero-title">
                        استكشف المحاضرات التعليمية
                    </h1>
                    <p class="hero-subtitle">
                        مجموعة شاملة من المحاضرات التعليمية المتاحة في النظام
                    </p>
                    {% if user.user_type == 'admin' or user.user_type == 'lecturer' %}
                    <div class="hero-actions">
                        {% if user.user_type == 'admin' %}
                            <a href="{% url 'admin:lectures_lecture_add' %}" class="btn btn-primary btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>
                                إضافة محاضرة جديدة
                            </a>
                        {% elif user.user_type == 'lecturer' %}
                            <a href="{% url 'lectures:add_lecture' %}" class="btn btn-primary btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>
                                إضافة محاضرة جديدة
                            </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-illustration">
                    <div class="floating-card">
                        <i class="bi bi-play-circle-fill"></i>
                        <span>{{ lectures|length }}</span>
                        <small>محاضرة متاحة</small>
                    </div>
                    <div class="floating-card delay-1">
                        <i class="bi bi-people-fill"></i>
                        <span>متنوعة</span>
                        <small>للجميع</small>
                    </div>
                    <div class="floating-card delay-2">
                        <i class="bi bi-bookmark-star-fill"></i>
                        <span>مميزة</span>
                        <small>محتوى</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="main-content">
<div class="container py-5">

        {% if lectures %}
        <!-- Lectures Section Header -->
        <div class="section-header mb-5">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h2 class="section-title">
                        <i class="bi bi-collection me-2"></i>
                        المحاضرات المتاحة
                    </h2>
                    <p class="section-subtitle">استكشف مجموعة متنوعة من المحاضرات التعليمية</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="lectures-count">
                        <span class="count-number">{{ lectures|length }}</span>
                        <span class="count-label">محاضرة متاحة</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lectures Grid -->
        <div class="lectures-grid">
            {% for lecture in lectures %}
            <div class="lecture-card-modern">
                <!-- صورة المحاضرة -->
                {% if lecture.image %}
                <div class="lecture-image">
                    <img src="{{ lecture.image.url }}" alt="{{ lecture.title }}" class="lecture-cover-image">
                    <div class="lecture-overlay">
                        <div class="lecture-type-badge">
                            <i class="bi bi-play-circle-fill"></i>
                            <span>محاضرة</span>
                        </div>
                        <div class="lecture-date-badge">
                            {{ lecture.date|date:"d/m/Y" }}
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="lecture-card-header">
                    <div class="lecture-type-badge">
                        <i class="bi bi-play-circle-fill"></i>
                        <span>محاضرة</span>
                    </div>
                    <div class="lecture-date-badge">
                        {{ lecture.date|date:"d/m/Y" }}
                    </div>
                </div>
                {% endif %}

                <div class="lecture-card-body">
                    <h3 class="lecture-title">
                        <a href="{% url 'lectures:lecture_detail' lecture.pk %}">{{ lecture.title }}</a>
                    </h3>

                    <div class="lecture-meta">
                        <div class="meta-item">
                            <i class="bi bi-book ms-2"></i>
                            <span class="course-name">{{ lecture.course.name }}</span>
                        </div>
                        <div class="meta-item">
                            <i class="bi bi-code-square ms-2"></i>
                            <span class="course-code">{{ lecture.course.code }}</span>
                        </div>
                        <div class="meta-item">
                            <i class="bi bi-person ms-2"></i>
                            <span class="lecturer-name">{{ lecture.lecturer.full_name }}</span>
                        </div>
                    </div>

                    {% if lecture.description %}
                    <div class="lecture-description">
                        <p>{{ lecture.description|truncatewords:15 }}</p>
                    </div>
                    {% endif %}

                    <div class="lecture-timestamp">
                        <i class="bi bi-clock align-middle ms-2"></i>
                        <span>{{ lecture.date|timesince }} مضت</span>
                    </div>
                </div>

                <div class="lecture-card-footer">
                    <a href="{% url 'lectures:lecture_detail' lecture.pk %}" class="lecture-view-btn">
                        <i class="bi bi-eye me-2"></i>
                        عرض المحاضرة
                    </a>
                    <div class="lecture-actions">
                        {% if lecture.file %}
                        <a href="{{ lecture.file.url }}" target="_blank" class="lecture-download-btn" title="تحميل الملف">
                            <i class="bi bi-download"></i>
                        </a>
                        {% endif %}
                        {% if user.is_staff or lecture.lecturer == user %}
                        <a href="{% url 'lectures:update_lecture_image' lecture.id %}" class="lecture-image-btn" title="إدارة صورة المحاضرة">
                            <i class="bi bi-image"></i>
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        {% else %}
        <!-- Empty State -->
        <div class="empty-state-section">
            <div class="empty-state-content">
                <div class="empty-state-icon">
                    <i class="bi bi-journal-plus"></i>
                </div>
                <h3 class="empty-state-title">لا توجد محاضرات متاحة</h3>
                <p class="empty-state-description">
                    لم يتم إضافة أي محاضرات بعد. سيتم عرض المحاضرات هنا عند إضافتها من قبل المحاضرين.
                </p>

                {% if user.user_type == 'admin' or user.user_type == 'lecturer' %}
                <div class="empty-state-actions">
                    {% if user.user_type == 'admin' %}
                        <a href="{% url 'admin:lectures_lecture_add' %}" class="btn btn-primary btn-lg">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة أول محاضرة
                        </a>
                    {% elif user.user_type == 'lecturer' %}
                        <a href="{% url 'lectures:add_lecture' %}" class="btn btn-primary btn-lg">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة أول محاضرة
                        </a>
                    {% endif %}
                </div>
                {% endif %}

                <div class="empty-state-suggestions">
                    <h4>اقتراحات:</h4>
                    <ul>
                        <li>تصفح المقررات المتاحة</li>
                        <li>تواصل مع المحاضرين</li>
                        <li>راجع الإعلانات الجديدة</li>
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
</div>

{% endblock %}