from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings
from .forms import LoginForm, FirstLoginPasswordChangeForm, OTPVerificationForm
from .models import User, OTPCode

def login_view(request):
    if request.user.is_authenticated:
        return redirect('dashboard')
        
    if request.method == 'POST':
        form = LoginForm(request, data=request.POST)
        if form.is_valid():
            email = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=email, password=password)
            
            if user is not None:
                if not user.is_active:
                    messages.error(request, 'حسابك غير نشط. الرجاء التواصل مع الإدارة.')
                    return redirect('login')
                
                if not user.password_changed:
                    # First login, redirect to change password
                    request.session['user_id'] = user.id
                    return redirect('first_login_change_password')
                
                # Generate and send OTP
                otp = OTPCode.generate_otp(user)
                send_otp_email(user, otp.code)
                
                # Store user ID in session for OTP verification
                request.session['user_id'] = user.id
                return redirect('verify_otp')
            else:
                messages.error(request, 'البريد الإلكتروني أو كلمة المرور غير صحيحة.')
    else:
        form = LoginForm()
    
    return render(request, 'accounts/login.html', {'form': form})

def first_login_change_password(request):
    user_id = request.session.get('user_id')
    if not user_id:
        return redirect('login')
    
    user = User.objects.get(id=user_id)
    
    if request.method == 'POST':
        form = FirstLoginPasswordChangeForm(user, request.POST)
        if form.is_valid():
            form.save()
            user.password_changed = True
            user.save()
            
            messages.success(request, 'تم تغيير كلمة المرور بنجاح. الرجاء تسجيل الدخول مرة أخرى.')
            return redirect('login')
    else:
        form = FirstLoginPasswordChangeForm(user)
    
    return render(request, 'accounts/first_login_change_password.html', {'form': form})

def verify_otp(request):
    user_id = request.session.get('user_id')
    if not user_id:
        return redirect('login')
    
    user = User.objects.get(id=user_id)
    
    if request.method == 'POST':
        form = OTPVerificationForm(user, request.POST)
        if form.is_valid():
            # OTP is valid, log in the user
            login(request, user)
            
            # Clear session data
            if 'user_id' in request.session:
                del request.session['user_id']
            
            return redirect('dashboard')
    else:
        form = OTPVerificationForm(user)
    
    return render(request, 'accounts/verify_otp.html', {'form': form})

def resend_otp(request):
    user_id = request.session.get('user_id')
    if not user_id:
        return redirect('login')
    
    user = User.objects.get(id=user_id)
    
    # Generate and send new OTP
    otp = OTPCode.generate_otp(user)
    send_otp_email(user, otp.code)
    
    messages.success(request, 'تم إرسال رمز تحقق جديد إلى بريدك الإلكتروني.')
    return redirect('verify_otp')

@login_required
def logout_view(request):
    logout(request)
    return redirect('login')

def send_otp_email(user, otp_code):
    subject = 'رمز التحقق للدخول إلى نظام إدارة المحتوى التعليمي'
    message = f'''مرحباً {user.full_name}،

رمز التحقق الخاص بك هو: {otp_code}

هذا الرمز صالح لمدة 10 دقائق فقط.

مع تحيات،
فريق نظام إدارة المحتوى التعليمي
'''
    
    send_mail(
        subject,
        message,
        settings.EMAIL_HOST_USER,
        [user.email],
        fail_silently=False,
    )
# Create your views here.
