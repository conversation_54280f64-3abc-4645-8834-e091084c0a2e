from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings
from django.middleware.csrf import get_token
from .forms import LoginForm, FirstLoginPasswordChangeForm, OTPVerificationForm, UserProfileForm
from .models import User, OTPCode

def login_view(request):
    if request.user.is_authenticated:
        # إذا كان المستخدم لم يتحقق من otp بعد، أعد توجيهه للتحقق
        if not request.session.get('otp_verified', False):
            return redirect('accounts:verify_otp')
        return redirect('core:dashboard')

    if request.method == 'POST':
        print(f"POST data: {request.POST}")  # Debug
        print(f"CSRF token in POST: {request.POST.get('csrfmiddlewaretoken')}")  # Debug

        form = LoginForm(request, data=request.POST)
        if form.is_valid():
            email = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=email, password=password)

            if user is not None:
                if not user.is_active:
                    messages.error(request, 'حسابك غير نشط. الرجاء التواصل مع الإدارة.')
                    return redirect('accounts:login')

                if not user.password_changed:
                    login(request, user)
                    request.session.save()
                    user.last_session_key = request.session.session_key
                    user.save()
                    # First login, redirect to change password
                    request.session['user_id'] = user.id
                    return redirect('accounts:first_login_change_password')

                # Generate and send OTP
                otp = OTPCode.generate_otp(user)
                send_otp_email(user, otp.code)

                # Store user ID in session for OTP verification
                request.session['user_id'] = user.id

                # Add message based on environment
                if settings.DEBUG:
                    messages.info(request, 'تم إنشاء رمز التحقق. في بيئة التطوير، ستجد الرمز في نافذة الكونسول.')
                else:
                    messages.info(request, 'تم إرسال رمز التحقق إلى بريدك الإلكتروني.')

                return redirect('accounts:verify_otp')
            else:
                messages.error(request, 'البريد الإلكتروني أو كلمة المرور غير صحيحة.')
        else:
            print(f"Form errors: {form.errors}")  # Debug
            print(f"Form non-field errors: {form.non_field_errors()}")  # Debug
            messages.error(request, 'يرجى التحقق من البيانات المدخلة.')
    else:
        form = LoginForm()

    # تأكد من وجود CSRF token
    csrf_token = get_token(request)
    context = {
        'form': form,
        'csrf_token': csrf_token
    }
    return render(request, 'accounts/login.html', context)

@login_required
def first_login_change_password(request):
    user = request.user
    if user.password_changed:
        return redirect('core:dashboard')
    if request.method == 'POST':
        form = FirstLoginPasswordChangeForm(user=user, data=request.POST)
        if form.is_valid():
            form.save()
            user.password_changed = True
            user.save()
            messages.success(request, 'تم تغيير كلمة المرور بنجاح.')
            return redirect('profile')
    else:
        form = FirstLoginPasswordChangeForm(user=user)

    return render(request, 'accounts/first_login_change_password.html', {'form': form})

def verify_otp(request):
    user_id = request.session.get('user_id')
    if not user_id:
        return redirect('accounts:login')

    user = User.objects.get(id=user_id)

    if request.method == 'POST':
        print(f"OTP POST data: {request.POST}")  # Debug
        print(f"OTP CSRF token in POST: {request.POST.get('csrfmiddlewaretoken')}")  # Debug

        form = OTPVerificationForm(user, request.POST)
        if form.is_valid():
            # OTP is valid, log in the user
            login(request, user)
            request.session.save()
            user.last_session_key = request.session.session_key
            user.save()
            # Mark OTP as verified in session
            request.session['otp_verified'] = True
            # Clear session data
            if 'user_id' in request.session:
                del request.session['user_id']

            return redirect('core:dashboard')
        else:
            print(f"OTP Form errors: {form.errors}")  # Debug
            print(f"OTP Form non-field errors: {form.non_field_errors()}")  # Debug
            messages.error(request, 'يرجى التحقق من رمز التحقق المدخل.')
    else:
        form = OTPVerificationForm(user)

    # تأكد من وجود CSRF token
    csrf_token = get_token(request)
    context = {
        'form': form,
        'csrf_token': csrf_token,
        'user': user
    }
    return render(request, 'accounts/verify_otp.html', context)

def resend_otp(request):
    user_id = request.session.get('user_id')
    if not user_id:
        return redirect('accounts:login')

    user = User.objects.get(id=user_id)

    if request.method == 'POST':
        print(f"Resend OTP POST data: {request.POST}")  # Debug

    # Generate and send new OTP
    otp = OTPCode.generate_otp(user)
    send_otp_email(user, otp.code)

    # Add message based on environment
    if settings.DEBUG:
        messages.success(request, 'تم إنشاء رمز تحقق جديد. ستجد الرمز في نافذة الكونسول.')
    else:
        messages.success(request, 'تم إرسال رمز تحقق جديد إلى بريدك الإلكتروني.')

    return redirect('accounts:verify_otp')

@login_required
def logout_view(request):
    # عند تسجيل الخروج، أزل otp_verified من الجلسة
    if 'otp_verified' in request.session:
        del request.session['otp_verified']
    logout(request)
    return redirect('accounts:login')

def send_otp_email(user, otp_code):
    """إرسال رمز التحقق عبر البريد الإلكتروني"""
    subject = 'رمز التحقق للدخول إلى نظام إدارة المحتوى التعليمي'
    message = f'''مرحباً {user.full_name}،

رمز التحقق الخاص بك هو: {otp_code}

هذا الرمز صالح لمدة 10 دقائق فقط.

مع تحيات،
فريق نظام إدارة المحتوى التعليمي
'''

    try:
        send_mail(
            subject,
            message,
            settings.EMAIL_HOST_USER,
            [user.email],
            fail_silently=False,
        )

        # في بيئة التطوير، اطبع الرمز في الكونسول للتسهيل
        if settings.DEBUG:
            print(f"\n{'='*50}")
            print(f"رمز التحقق للمستخدم {user.email}: {otp_code}")
            print(f"{'='*50}\n")

    except Exception as e:
        # في حالة فشل إرسال البريد، اطبع الرمز في الكونسول
        print(f"\n{'='*50}")
        print(f"فشل في إرسال البريد الإلكتروني: {str(e)}")
        print(f"رمز التحقق للمستخدم {user.email}: {otp_code}")
        print(f"{'='*50}\n")

        # في بيئة التطوير، لا نريد أن يتوقف النظام
        if settings.DEBUG:
            pass
        else:
            raise

# View مبسط للاختبار
def simple_login_view(request):
    """View مبسط لاختبار تسجيل الدخول بدون OTP"""
    if request.user.is_authenticated:
        return redirect('core:dashboard')

    if request.method == 'POST':
        email = request.POST.get('username')
        password = request.POST.get('password')

        print(f"Attempting login with email: {email}")

        user = authenticate(request, username=email, password=password)
        if user is not None:
            login(request, user)
            request.session.save()  # تأكد من إنشاء session_key
            user.last_session_key = request.session.session_key
            user.save()
            messages.success(request, 'تم تسجيل الدخول بنجاح!')
            return redirect('dashboard')
        else:
            messages.error(request, 'البريد الإلكتروني أو كلمة المرور غير صحيحة.')

    return render(request, 'accounts/simple_login.html')

@login_required
def profile_view(request):
    user = request.user
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=user)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الملف الشخصي بنجاح.')
            return redirect('accounts:profile')
        else:
            messages.error(request, 'حدث خطأ أثناء تحديث البيانات. يرجى التحقق من الحقول المدخلة.')
    else:
        form = UserProfileForm(instance=user)
    return render(request, 'accounts/profile.html', {'form': form})

# Create your views here.
