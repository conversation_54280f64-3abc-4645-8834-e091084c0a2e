from django.db import models

class Faculty(models.Model):
    name = models.<PERSON><PERSON><PERSON><PERSON>('اسم الكلية', max_length=255)
    is_active = models.BooleanField('نشطة', default=True)
    
    class Meta:
        verbose_name = 'كلية'
        verbose_name_plural = 'الكليات'
    
    def __str__(self):
        return self.name

class Department(models.Model):
    name = models.Char<PERSON>ield('اسم القسم', max_length=255)
    faculty = models.ForeignKey(Faculty, on_delete=models.CASCADE, related_name='departments', verbose_name='الكلية')
    is_active = models.Bo<PERSON>anField('نشط', default=True)
    
    class Meta:
        verbose_name = 'قسم'
        verbose_name_plural = 'الأقسام'
    
    def __str__(self):
        return f"{self.name} - {self.faculty.name}"
