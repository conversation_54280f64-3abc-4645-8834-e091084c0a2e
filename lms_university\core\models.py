from django.db import models

class Faculty(models.Model):
    name = models.Cha<PERSON><PERSON><PERSON>('اسم الكلية', max_length=255)
    is_active = models.BooleanField('نشطة', default=True)

    class Meta:
        verbose_name = 'كلية'
        verbose_name_plural = 'الكليات'

    def __str__(self):
        return self.name

class Department(models.Model):
    name = models.CharField('اسم القسم', max_length=255)
    faculty = models.ForeignKey(Faculty, on_delete=models.CASCADE, related_name='departments', verbose_name='الكلية')
    is_active = models.BooleanField('نشط', default=True)

    class Meta:
        verbose_name = 'قسم'
        verbose_name_plural = 'الأقسام'

    def __str__(self):
        return f"{self.name} - {self.faculty.name}"

class Course(models.Model):
    name = models.CharField('اسم المقرر', max_length=255)
    code = models.Cha<PERSON><PERSON><PERSON>('رمز المقرر', max_length=20, unique=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='courses', verbose_name='القسم')
    description = models.TextField('وصف المقرر', blank=True)
    credit_hours = models.PositiveIntegerField('الساعات المعتمدة', default=3)
    image = models.ImageField('صورة المقرر', upload_to='course_images/', blank=True, null=True, help_text='صورة غلاف المقرر')
    is_active = models.BooleanField('نشط', default=True)

    class Meta:
        verbose_name = 'مقرر'
        verbose_name_plural = 'المقررات'

    def __str__(self):
        return f"{self.code} - {self.name}"

    def get_assigned_lecturers(self):
        """الحصول على المحاضرين المسندين لهذا المقرر"""
        return CourseAssignment.objects.filter(
            course=self,
            is_active=True
        ).select_related('lecturer')

    def is_lecturer_assigned(self, lecturer):
        """التحقق من إسناد المحاضر لهذا المقرر"""
        return CourseAssignment.objects.filter(
            course=self,
            lecturer=lecturer,
            is_active=True
        ).exists()

class CourseEnrollment(models.Model):
    """تسجيل الطلاب في المقررات"""

    ENROLLMENT_STATUS_CHOICES = [
        ('enrolled', 'مسجل'),
        ('pending', 'في انتظار الموافقة'),
        ('dropped', 'منسحب'),
        ('completed', 'مكتمل'),
    ]

    student = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'student'},
        verbose_name='الطالب',
        related_name='course_enrollments'
    )
    course = models.ForeignKey(
        Course,
        on_delete=models.CASCADE,
        verbose_name='المقرر',
        related_name='enrollments'
    )
    enrollment_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ التسجيل'
    )
    status = models.CharField(
        max_length=20,
        choices=ENROLLMENT_STATUS_CHOICES,
        default='enrolled',
        verbose_name='حالة التسجيل'
    )
    enrolled_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type': 'admin'},
        verbose_name='تم التسجيل بواسطة',
        related_name='enrolled_students'
    )
    notes = models.TextField(
        blank=True,
        verbose_name='ملاحظات'
    )

    class Meta:
        unique_together = ['student', 'course']
        verbose_name = 'تسجيل في مقرر'
        verbose_name_plural = 'تسجيلات المقررات'
        ordering = ['-enrollment_date']

    def __str__(self):
        return f"{self.student.full_name} - {self.course.name}"

    def can_access_course(self):
        """تحقق من إمكانية الوصول للمقرر"""
        return self.status == 'enrolled'

class LectureAccess(models.Model):
    """صلاحيات الوصول للمحاضرات"""

    ACCESS_TYPE_CHOICES = [
        ('granted', 'مسموح'),
        ('denied', 'ممنوع'),
        ('pending', 'في انتظار الموافقة'),
    ]

    student = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'student'},
        verbose_name='الطالب',
        related_name='lecture_accesses'
    )
    lecture = models.ForeignKey(
        'lectures.Lecture',
        on_delete=models.CASCADE,
        verbose_name='المحاضرة',
        related_name='access_permissions'
    )
    access_type = models.CharField(
        max_length=20,
        choices=ACCESS_TYPE_CHOICES,
        default='granted',
        verbose_name='نوع الصلاحية'
    )
    granted_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ منح الصلاحية'
    )
    granted_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type__in': ['admin', 'lecturer']},
        verbose_name='تم منح الصلاحية بواسطة',
        related_name='granted_lecture_accesses'
    )
    expiry_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='تاريخ انتهاء الصلاحية'
    )
    notes = models.TextField(
        blank=True,
        verbose_name='ملاحظات'
    )

    class Meta:
        unique_together = ['student', 'lecture']
        verbose_name = 'صلاحية وصول للمحاضرة'
        verbose_name_plural = 'صلاحيات الوصول للمحاضرات'
        ordering = ['-granted_date']

    def __str__(self):
        return f"{self.student.full_name} - {self.lecture.title} ({self.get_access_type_display()})"

    def is_active(self):
        """تحقق من صحة الصلاحية"""
        if self.access_type != 'granted':
            return False

        if self.expiry_date:
            from django.utils import timezone
            return timezone.now() <= self.expiry_date

        return True


class CourseAssignment(models.Model):
    """إسناد المقررات للمحاضرين"""

    course = models.ForeignKey(
        Course,
        on_delete=models.CASCADE,
        verbose_name='المقرر',
        related_name='assignments'
    )
    lecturer = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'lecturer'},
        verbose_name='المحاضر',
        related_name='course_assignments'
    )
    assigned_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type': 'admin'},
        verbose_name='تم الإسناد بواسطة',
        related_name='assigned_courses'
    )
    assignment_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإسناد'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name='تاريخ البداية'
    )
    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name='تاريخ النهاية'
    )
    notes = models.TextField(
        blank=True,
        verbose_name='ملاحظات'
    )

    class Meta:
        unique_together = ['course', 'lecturer']
        verbose_name = 'إسناد مقرر'
        verbose_name_plural = 'إسناد المقررات'
        ordering = ['-assignment_date']

    def __str__(self):
        return f"{self.lecturer.full_name} - {self.course.name}"

    def is_assignment_active(self):
        """التحقق من صحة الإسناد"""
        if not self.is_active:
            return False

        from django.utils import timezone
        now = timezone.now().date()

        if self.start_date and now < self.start_date:
            return False

        if self.end_date and now > self.end_date:
            return False

        return True
