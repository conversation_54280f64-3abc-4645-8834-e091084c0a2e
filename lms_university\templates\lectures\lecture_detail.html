{% extends 'base.html' %}

{% block title %}{{ lecture.title }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="hero-content">
                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="hero-breadcrumb">
                        <ol class="breadcrumb-modern">
                            <li class="breadcrumb-item">
                                <a href="{% url 'dashboard' %}">
                                    <i class="bi bi-house-fill me-1"></i>الرئيسية
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{% url 'lectures:lecture_list' %}">المحاضرات</a>
                            </li>
                            <li class="breadcrumb-item active">{{ lecture.title }}</li>
                        </ol>
                    </nav>

                    <div class="welcome-badge">
                        <i class="bi bi-play-circle-fill me-2"></i>
                        محاضرة تعليمية
                    </div>
                    <h1 class="hero-title">
                        {{ lecture.title }}
                    </h1>
                    <p class="hero-subtitle">
                        {{ lecture.course.name }} - {{ lecture.lecturer.full_name }}
                    </p>
                    <div class="hero-actions">
                        {% if lecture.file %}
                        <a href="{{ lecture.file.url }}" class="btn btn-primary btn-lg me-3" target="_blank">
                            <i class="bi bi-eye me-2"></i>
                            عرض المحاضرة
                        </a>
                        <a href="{{ lecture.file.url }}" class="btn btn-outline-light btn-lg" download>
                            <i class="bi bi-download me-2"></i>
                            تحميل الملف
                        </a>
                        {% else %}
                        <a href="{% url 'lectures:lecture_list' %}" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-arrow-left me-2"></i>
                            العودة للمحاضرات
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-info">
                    <div class="lecture-preview-card">
                        <div class="preview-icon">
                            <i class="bi bi-journal-text"></i>
                        </div>
                        <div class="preview-content">
                            <h4>{{ lecture.course.code }}</h4>
                            <p>{{ lecture.date|date:"d/m/Y" }}</p>
                            <div class="lecture-stats">
                                <div class="stat-item">
                                    <i class="bi bi-clock me-1"></i>
                                    {{ lecture.date|date:"H:i" }}
                                </div>
                                {% if lecture.file %}
                                <div class="stat-item">
                                    <i class="bi bi-file-earmark-check me-1"></i>
                                    ملف متاح
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="main-content">
<div class="container py-5">

    <!-- Lecture Information Section -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <!-- Lecture Details Card -->
            <div class="lecture-details-card">
                <div class="details-header">
                    <div class="header-info">
                        <h3 class="details-title">
                            <i class="bi bi-info-circle me-2"></i>
                            معلومات المحاضرة
                        </h3>
                        <p class="details-subtitle">تفاصيل شاملة عن المحاضرة والمقرر</p>
                    </div>
                    {% if user == lecture.lecturer or user.user_type == 'admin' %}
                    <div class="header-actions">
                        <a href="{% url 'lectures:edit_lecture' lecture.pk %}" class="action-btn edit">
                            <i class="bi bi-pencil me-2"></i>
                            تعديل المحاضرة
                        </a>
                    </div>
                    {% endif %}
                </div>

                <div class="details-body">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon course">
                                <i class="bi bi-book"></i>
                            </div>
                            <div class="info-content">
                                <h4>المقرر</h4>
                                <p>{{ lecture.course.name }}</p>
                                <span class="info-badge">{{ lecture.course.code }}</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon department">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="info-content">
                                <h4>القسم</h4>
                                <p>{{ lecture.course.department.name }}</p>
                                <span class="info-badge">{{ lecture.course.department.faculty.name }}</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon lecturer">
                                <i class="bi bi-person-circle"></i>
                            </div>
                            <div class="info-content">
                                <h4>المحاضر</h4>
                                <p>{{ lecture.lecturer.full_name }}</p>
                                <span class="info-badge">{{ lecture.lecturer.email }}</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon date">
                                <i class="bi bi-calendar-event"></i>
                            </div>
                            <div class="info-content">
                                <h4>تاريخ الرفع</h4>
                                <p>{{ lecture.date|date:"d/m/Y" }}</p>
                                <span class="info-badge">{{ lecture.date|date:"H:i" }}</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon credits">
                                <i class="bi bi-award"></i>
                            </div>
                            <div class="info-content">
                                <h4>الساعات المعتمدة</h4>
                                <p>{{ lecture.course.credit_hours }} ساعة</p>
                                <span class="info-badge">معتمدة</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon status">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="info-content">
                                <h4>حالة المحاضرة</h4>
                                <p>متاحة للطلاب</p>
                                {% if lecture.file %}
                                <span class="info-badge success">ملف متاح</span>
                                {% else %}
                                <span class="info-badge warning">لا يوجد ملف</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Quick Actions Sidebar -->
            <div class="quick-actions-sidebar">
                <div class="actions-card">
                    <div class="actions-header">
                        <i class="bi bi-lightning"></i>
                        <h4>إجراءات سريعة</h4>
                    </div>
                    <div class="actions-content">
                        {% if lecture.file %}
                        <a href="{{ lecture.file.url }}" class="quick-action-btn primary" target="_blank">
                            <i class="bi bi-eye"></i>
                            <span>عرض المحاضرة</span>
                        </a>
                        <a href="{{ lecture.file.url }}" class="quick-action-btn success" download>
                            <i class="bi bi-download"></i>
                            <span>تحميل الملف</span>
                        </a>
                        {% endif %}

                        <a href="{% url 'lectures:lecture_list' %}" class="quick-action-btn secondary">
                            <i class="bi bi-arrow-left"></i>
                            <span>العودة للمحاضرات</span>
                        </a>

                        {% if user == lecture.lecturer or user.user_type == 'admin' %}
                        <a href="{% url 'lectures:edit_lecture' lecture.pk %}" class="quick-action-btn warning">
                            <i class="bi bi-pencil"></i>
                            <span>تعديل المحاضرة</span>
                        </a>
                        {% endif %}
                    </div>
                </div>

                <!-- Course Info Card -->
                <div class="course-info-card">
                    <div class="course-header">
                        <i class="bi bi-book-half"></i>
                        <h4>معلومات المقرر</h4>
                    </div>
                    <div class="course-content">
                        <div class="course-item">
                            <span class="course-label">رمز المقرر:</span>
                            <span class="course-value">{{ lecture.course.code }}</span>
                        </div>
                        <div class="course-item">
                            <span class="course-label">الساعات:</span>
                            <span class="course-value">{{ lecture.course.credit_hours }}</span>
                        </div>
                        <div class="course-item">
                            <span class="course-label">القسم:</span>
                            <span class="course-value">{{ lecture.course.department.name }}</span>
                        </div>
                        <div class="course-item">
                            <span class="course-label">الكلية:</span>
                            <span class="course-value">{{ lecture.course.department.faculty.name }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Description and File Section -->
    <div class="row">
        <div class="col-12">
            {% if lecture.description %}
            <!-- Description Card -->
            <div class="description-card mb-4">
                <div class="description-header">
                    <div class="description-icon">
                        <i class="bi bi-file-text"></i>
                    </div>
                    <div class="description-title">
                        <h3>وصف المحاضرة</h3>
                        <p>تفاصيل ومحتوى المحاضرة</p>
                    </div>
                </div>
                <div class="description-content">
                    <div class="description-text">
                        {{ lecture.description|linebreaks }}
                    </div>
                </div>
            </div>
            {% endif %}

            {% if lecture.file %}
            <!-- File Download Card -->
            <div class="file-download-card">
                <div class="file-header">
                    <div class="file-icon">
                        <i class="bi bi-file-earmark-arrow-down"></i>
                    </div>
                    <div class="file-info">
                        <h3>ملف المحاضرة</h3>
                        <p>{{ lecture.file.name|cut:"lectures/" }}</p>
                    </div>
                    <div class="file-actions">
                        <a href="{{ lecture.file.url }}" class="file-action-btn preview" target="_blank">
                            <i class="bi bi-eye me-2"></i>
                            معاينة
                        </a>
                        <a href="{{ lecture.file.url }}" class="file-action-btn download" download>
                            <i class="bi bi-download me-2"></i>
                            تحميل
                        </a>
                    </div>
                </div>

                <div class="file-details">
                    <div class="file-stats">
                        <div class="stat-item">
                            <i class="bi bi-file-earmark-check text-success"></i>
                            <span>ملف متاح للتحميل</span>
                        </div>
                        <div class="stat-item">
                            <i class="bi bi-shield-check text-primary"></i>
                            <span>محتوى آمن ومعتمد</span>
                        </div>
                        <div class="stat-item">
                            <i class="bi bi-clock text-info"></i>
                            <span>متاح على مدار الساعة</span>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <!-- No File Available -->
            <div class="no-file-card">
                <div class="no-file-content">
                    <div class="no-file-icon">
                        <i class="bi bi-file-earmark-x"></i>
                    </div>
                    <h3>لا يوجد ملف متاح</h3>
                    <p>لم يتم رفع ملف لهذه المحاضرة بعد</p>
                    {% if user == lecture.lecturer or user.user_type == 'admin' %}
                    <a href="{% url 'lectures:edit_lecture' lecture.pk %}" class="btn-modern btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة ملف للمحاضرة
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
</div>

{% block extra_css %}
<style>
    /* Global Styles */
    body {
        padding-top: 0 !important;
        font-family: 'Cairo', Arial, Tahoma, sans-serif !important;
    }

    /* Hero Section */
    .hero-section {
        background: linear-gradient(135deg, #5260bf 0%, #764ba2 100%);
        color: white;
        padding: 6rem 0 4rem 0;
        margin-top: 0;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    }

    /* Main Content */
    .main-content {
        position: relative;
        z-index: 2;
        background: #f8f9fa;
        margin-top: -2rem;
        border-radius: 2rem 2rem 0 0;
    }

    /* Hero Content */
    .hero-content {
        position: relative;
        z-index: 2;
    }

    /* Hero Breadcrumb */
    .hero-breadcrumb {
        margin-bottom: 1.5rem;
    }

    .breadcrumb-modern {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 25px;
        padding: 0.5rem 1rem;
        margin: 0;
        list-style: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .breadcrumb-item {
        display: flex;
        align-items: center;
    }

    .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a:hover {
        color: white;
    }

    .breadcrumb-item.active {
        color: rgba(255, 255, 255, 0.7);
        font-weight: 500;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
        color: rgba(255, 255, 255, 0.6);
        margin: 0 0.5rem;
    }

    .welcome-badge {
        display: inline-flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        backdrop-filter: blur(10px);
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .hero-actions .btn {
        padding: 0.75rem 2rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .hero-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }

    /* Hero Info */
    .hero-info {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    .lecture-preview-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        width: 100%;
    }

    .lecture-preview-card:hover {
        transform: scale(1.05);
        background: rgba(255, 255, 255, 0.25);
    }

    .preview-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin: 0 auto 1rem;
    }

    .preview-content h4 {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .preview-content p {
        opacity: 0.9;
        margin-bottom: 1rem;
    }

    .lecture-stats {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    /* Lecture Details Card */
    .lecture-details-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .details-header {
        padding: 2rem 2rem 1rem;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .details-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .details-subtitle {
        color: #718096;
        font-size: 0.9rem;
        margin: 0;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
        text-decoration: none;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(82, 96, 191, 0.3);
        color: white;
    }

    .details-body {
        padding: 2rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .info-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1.5rem;
        background: #f7fafc;
        border-radius: 15px;
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .info-item:hover {
        background: white;
        border-color: #5260bf;
        box-shadow: 0 5px 15px rgba(82, 96, 191, 0.1);
    }

    .info-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
        flex-shrink: 0;
    }

    .info-icon.course {
        background: linear-gradient(135deg, #5260bf, #667eea);
    }

    .info-icon.department {
        background: linear-gradient(135deg, #764ba2, #9b59b6);
    }

    .info-icon.lecturer {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .info-icon.date {
        background: linear-gradient(135deg, #17a2b8, #6f42c1);
    }

    .info-icon.credits {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
    }

    .info-icon.status {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .info-content h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .info-content p {
        color: #4a5568;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .info-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        background: #e2e8f0;
        color: #4a5568;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .info-badge.success {
        background: #d4edda;
        color: #155724;
    }

    .info-badge.warning {
        background: #fff3cd;
        color: #856404;
    }

    /* Quick Actions Sidebar */
    .quick-actions-sidebar {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .actions-card, .course-info-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .actions-header, .course-header {
        padding: 1.5rem 1.5rem 1rem;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .actions-header i, .course-header i {
        width: 35px;
        height: 35px;
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .actions-header h4, .course-header h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
    }

    .actions-content, .course-content {
        padding: 1.5rem;
    }

    .quick-action-btn {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-bottom: 0.75rem;
        border: 2px solid transparent;
    }

    .quick-action-btn:last-child {
        margin-bottom: 0;
    }

    .quick-action-btn i {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .quick-action-btn.primary {
        background: #e3f2fd;
        color: #1976d2;
    }

    .quick-action-btn.primary i {
        background: #1976d2;
        color: white;
    }

    .quick-action-btn.primary:hover {
        background: #1976d2;
        color: white;
        border-color: #1976d2;
    }

    .quick-action-btn.success {
        background: #e8f5e8;
        color: #28a745;
    }

    .quick-action-btn.success i {
        background: #28a745;
        color: white;
    }

    .quick-action-btn.success:hover {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }

    .quick-action-btn.secondary {
        background: #f8f9fa;
        color: #6c757d;
    }

    .quick-action-btn.secondary i {
        background: #6c757d;
        color: white;
    }

    .quick-action-btn.secondary:hover {
        background: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    .quick-action-btn.warning {
        background: #fff3e0;
        color: #f57c00;
    }

    .quick-action-btn.warning i {
        background: #f57c00;
        color: white;
    }

    .quick-action-btn.warning:hover {
        background: #f57c00;
        color: white;
        border-color: #f57c00;
    }

    .course-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f1f5f9;
    }

    .course-item:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }

    .course-label {
        font-size: 0.8rem;
        color: #718096;
        font-weight: 500;
    }

    .course-value {
        font-size: 0.9rem;
        color: #2d3748;
        font-weight: 600;
    }

    /* Description Card */
    .description-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .description-header {
        padding: 2rem 2rem 1rem;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .description-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .description-title h3 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .description-title p {
        color: #718096;
        font-size: 0.9rem;
        margin: 0;
    }

    .description-content {
        padding: 2rem;
    }

    .description-text {
        font-size: 1rem;
        line-height: 1.8;
        color: #4a5568;
    }

    /* File Download Card */
    .file-download-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .file-header {
        padding: 2rem;
        background: linear-gradient(135deg, #e8f5e8, #d4edda);
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .file-icon {
        width: 80px;
        height: 80px;
        background: #28a745;
        color: white;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        flex-shrink: 0;
    }

    .file-info {
        flex-grow: 1;
    }

    .file-info h3 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .file-info p {
        color: #4a5568;
        margin: 0;
        font-size: 1rem;
    }

    .file-actions {
        display: flex;
        gap: 0.75rem;
        flex-shrink: 0;
    }

    .file-action-btn {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .file-action-btn.preview {
        background: #17a2b8;
        color: white;
    }

    .file-action-btn.download {
        background: #28a745;
        color: white;
    }

    .file-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }

    .file-details {
        padding: 2rem;
        border-top: 1px solid #e2e8f0;
    }

    .file-stats {
        display: flex;
        justify-content: space-around;
        gap: 1rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        font-weight: 500;
        color: #4a5568;
    }

    /* No File Card */
    .no-file-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
        padding: 4rem 2rem;
        text-align: center;
    }

    .no-file-icon {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #f7fafc, #e2e8f0);
        border-radius: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        color: #718096;
        font-size: 3rem;
    }

    .no-file-content h3 {
        font-size: 1.75rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 1rem;
    }

    .no-file-content p {
        color: #718096;
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .btn-modern {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 1rem;
    }

    .btn-modern.btn-primary {
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
    }

    .btn-modern.btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(82, 96, 191, 0.3);
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-section {
            padding: 4rem 0 3rem 0;
        }

        .hero-title {
            font-size: 2rem;
        }

        .hero-subtitle {
            font-size: 1rem;
        }

        .hero-actions .btn {
            display: block;
            margin-bottom: 1rem;
            width: 100%;
        }

        .lecture-preview-card {
            margin-top: 2rem;
        }

        .main-content {
            margin-top: -1rem;
            border-radius: 1rem 1rem 0 0;
        }

        .info-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .details-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .file-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .file-actions {
            align-self: stretch;
            justify-content: center;
        }

        .file-stats {
            flex-direction: column;
            gap: 0.75rem;
        }

        .quick-actions-sidebar {
            margin-top: 2rem;
        }
    }

    @media (max-width: 576px) {
        .hero-section {
            padding: 3rem 0 2rem 0;
        }

        .hero-title {
            font-size: 1.75rem;
        }

        .breadcrumb-modern {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            display: none;
        }

        .details-body {
            padding: 1.5rem;
        }

        .info-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .description-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .description-content {
            padding: 1.5rem;
        }

        .file-action-btn {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .actions-content,
        .course-content {
            padding: 1rem;
        }

        .quick-action-btn {
            padding: 0.75rem;
        }
    }
</style>
{% endblock %}
{% endblock %}
