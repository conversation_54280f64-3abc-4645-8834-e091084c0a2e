{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="hero-content">
                    <div class="welcome-badge">
                        <i class="bi bi-plus-circle-fill me-2"></i>
                        إضافة محاضرة جديدة
                    </div>
                    <h1 class="hero-title">
                        إنشاء محاضرة جديدة
                    </h1>
                    <p class="hero-subtitle">
                        أضف محاضرة جديدة لطلابك مع المحتوى التعليمي المناسب
                    </p>
                    <div class="hero-actions">
                        <a href="{% url 'lectures:lecturer_lectures' %}" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-arrow-left me-2"></i>
                            العودة للمحاضرات
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-illustration">
                    <div class="floating-card">
                        <i class="bi bi-journal-plus"></i>
                        <span>جديد</span>
                        <small>محاضرة</small>
                    </div>
                    <div class="floating-card delay-1">
                        <i class="bi bi-upload"></i>
                        <span>رفع</span>
                        <small>ملف</small>
                    </div>
                    <div class="floating-card delay-2">
                        <i class="bi bi-check-circle"></i>
                        <span>حفظ</span>
                        <small>نشر</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="main-content">
<div class="container py-5">

    <!-- Enhanced Form Section -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Form Progress -->
            <div class="form-progress-section mb-4">
                <div class="progress-steps">
                    <div class="step active">
                        <div class="step-icon">
                            <i class="bi bi-info-circle"></i>
                        </div>
                        <span>معلومات أساسية</span>
                    </div>
                    <div class="step">
                        <div class="step-icon">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                        <span>المحتوى</span>
                    </div>
                    <div class="step">
                        <div class="step-icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <span>المراجعة والحفظ</span>
                    </div>
                </div>
            </div>

            <!-- Main Form Card -->
            <div class="form-card">
                <div class="form-header">
                    <div class="header-info">
                        <h3 class="form-title">
                            <i class="bi bi-journal-plus me-2"></i>
                            معلومات المحاضرة
                        </h3>
                        <p class="form-subtitle">أدخل تفاصيل المحاضرة الجديدة</p>
                    </div>
                </div>

                <div class="form-body">
                    <form method="post" enctype="multipart/form-data" id="lectureForm" class="modern-form">
                        {% csrf_token %}

                        <!-- Course Selection -->
                        <div class="form-group-modern">
                            <div class="input-group-modern">
                                <div class="input-icon">
                                    <i class="bi bi-book"></i>
                                </div>
                                <div class="input-content">
                                    <label for="{{ form.course.id_for_label }}" class="modern-label">
                                        {{ form.course.label }}
                                        <span class="required-indicator">*</span>
                                    </label>
                                    {{ form.course }}
                                    {% if form.course.help_text %}
                                    <div class="input-help">{{ form.course.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            {% if form.course.errors %}
                            <div class="input-error">
                                {% for error in form.course.errors %}
                                <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Title -->
                        <div class="form-group-modern">
                            <div class="input-group-modern">
                                <div class="input-icon">
                                    <i class="bi bi-type"></i>
                                </div>
                                <div class="input-content">
                                    <label for="{{ form.title.id_for_label }}" class="modern-label">
                                        {{ form.title.label }}
                                        <span class="required-indicator">*</span>
                                    </label>
                                    {{ form.title }}
                                    {% if form.title.help_text %}
                                    <div class="input-help">{{ form.title.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            {% if form.title.errors %}
                            <div class="input-error">
                                {% for error in form.title.errors %}
                                <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Description -->
                        <div class="form-group-modern">
                            <div class="input-group-modern">
                                <div class="input-icon">
                                    <i class="bi bi-text-paragraph"></i>
                                </div>
                                <div class="input-content">
                                    <label for="{{ form.description.id_for_label }}" class="modern-label">
                                        {{ form.description.label }}
                                    </label>
                                    {{ form.description }}
                                    {% if form.description.help_text %}
                                    <div class="input-help">{{ form.description.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            {% if form.description.errors %}
                            <div class="input-error">
                                {% for error in form.description.errors %}
                                <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- File Upload -->
                        <div class="form-group-modern">
                            <div class="file-upload-modern">
                                <div class="upload-header">
                                    <div class="upload-icon">
                                        <i class="bi bi-cloud-upload"></i>
                                    </div>
                                    <div class="upload-content">
                                        <label for="{{ form.file.id_for_label }}" class="modern-label">
                                            {{ form.file.label }}
                                        </label>
                                        <p class="upload-subtitle">اسحب الملف هنا أو انقر للاختيار</p>
                                    </div>
                                </div>
                                <div class="upload-area" id="uploadArea">
                                    {{ form.file }}
                                    <div class="upload-placeholder">
                                        <i class="bi bi-file-earmark-plus"></i>
                                        <span>اختر ملف المحاضرة</span>
                                        <small>PDF, Word, PowerPoint, أو ملفات مضغوطة</small>
                                    </div>
                                </div>
                                <div class="upload-info" id="uploadInfo">
                                    {% if form.file.help_text %}
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        {{ form.file.help_text }}
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                            {% if form.file.errors %}
                            <div class="input-error">
                                {% for error in form.file.errors %}
                                <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Image Upload -->
                        <div class="form-group-modern">
                            <div class="file-upload-modern">
                                <div class="upload-header">
                                    <div class="upload-icon">
                                        <i class="bi bi-image"></i>
                                    </div>
                                    <div class="upload-content">
                                        <label for="{{ form.image.id_for_label }}" class="modern-label">
                                            {{ form.image.label }}
                                        </label>
                                        <p class="upload-subtitle">اسحب الصورة هنا أو انقر للاختيار (اختياري)</p>
                                    </div>
                                </div>
                                <div class="upload-area" id="imageUploadArea">
                                    {{ form.image }}
                                    <div class="upload-placeholder">
                                        <i class="bi bi-image-fill"></i>
                                        <span>اختر صورة غلاف للمحاضرة</span>
                                        <small>JPEG, PNG, GIF, WebP</small>
                                    </div>
                                </div>
                                <div class="upload-info" id="imageUploadInfo">
                                    {% if form.image.help_text %}
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        {{ form.image.help_text }}
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                            {% if form.image.errors %}
                            <div class="input-error">
                                {% for error in form.image.errors %}
                                <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- General Form Errors -->
                        {% if form.non_field_errors %}
                        <div class="form-errors">
                            {% for error in form.non_field_errors %}
                            <div class="error-item">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Submit Buttons -->
                        <div class="form-actions">
                            <a href="{% url 'lectures:lecturer_lectures' %}" class="btn-modern btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>
                                إلغاء والعودة
                            </a>
                            <button type="submit" class="btn-modern btn-primary" id="submitBtn">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ ونشر المحاضرة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-2">
            <div class="side-panel">
                <!-- Quick Tips -->
                <div class="tips-card">
                    <div class="tips-header">
                        <i class="bi bi-lightbulb"></i>
                        <h4>نصائح سريعة</h4>
                    </div>
                    <div class="tips-content">
                        <div class="tip-item">
                            <i class="bi bi-check-circle"></i>
                            <span>اختر عنواناً واضحاً</span>
                        </div>
                        <div class="tip-item">
                            <i class="bi bi-check-circle"></i>
                            <span>أضف وصفاً مفيداً</span>
                        </div>
                        <div class="tip-item">
                            <i class="bi bi-check-circle"></i>
                            <span>حجم الملف أقل من 50MB</span>
                        </div>
                        <div class="tip-item">
                            <i class="bi bi-check-circle"></i>
                            <span>تنسيقات مدعومة متعددة</span>
                        </div>
                    </div>
                </div>

                <!-- File Types -->
                <div class="file-types-card">
                    <div class="file-types-header">
                        <i class="bi bi-file-earmark"></i>
                        <h4>أنواع الملفات</h4>
                    </div>
                    <div class="file-types-content">
                        <div class="file-type">
                            <i class="bi bi-file-earmark-pdf text-danger"></i>
                            <span>PDF</span>
                        </div>
                        <div class="file-type">
                            <i class="bi bi-file-earmark-word text-primary"></i>
                            <span>Word</span>
                        </div>
                        <div class="file-type">
                            <i class="bi bi-file-earmark-ppt text-warning"></i>
                            <span>PowerPoint</span>
                        </div>
                        <div class="file-type">
                            <i class="bi bi-file-earmark-zip text-success"></i>
                            <span>مضغوط</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Global Styles */
    body {
        padding-top: 0 !important;
        font-family: 'Cairo', Arial, Tahoma, sans-serif !important;
    }

    /* Hero Section */
    .hero-section {
        background: linear-gradient(135deg, #5260bf 0%, #764ba2 100%);
        color: white;
        padding: 6rem 0 4rem 0;
        margin-top: 0;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    }

    /* Main Content */
    .main-content {
        position: relative;
        z-index: 2;
        background: #f8f9fa;
        margin-top: -2rem;
        border-radius: 2rem 2rem 0 0;
    }

    /* Hero Content */
    .hero-content {
        position: relative;
        z-index: 2;
    }

    .welcome-badge {
        display: inline-flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        backdrop-filter: blur(10px);
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .hero-actions .btn {
        padding: 0.75rem 2rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .hero-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }

    /* Floating Cards Animation */
    .hero-illustration {
        position: relative;
        height: 300px;
    }

    .floating-card {
        position: absolute;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 1.5rem;
        text-align: center;
        animation: float 6s ease-in-out infinite;
        transition: all 0.3s ease;
    }

    .floating-card:hover {
        transform: scale(1.05);
        background: rgba(255, 255, 255, 0.25);
    }

    .floating-card:nth-child(1) {
        top: 20px;
        right: 20px;
        animation-delay: 0s;
    }

    .floating-card:nth-child(2) {
        top: 120px;
        left: 20px;
        animation-delay: 2s;
    }

    .floating-card:nth-child(3) {
        bottom: 20px;
        right: 50px;
        animation-delay: 4s;
    }

    .floating-card i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: block;
    }

    .floating-card span {
        font-size: 1.5rem;
        font-weight: bold;
        display: block;
    }

    .floating-card small {
        opacity: 0.8;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    /* Form Progress */
    .form-progress-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background: #e2e8f0;
        z-index: 1;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .step-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #e2e8f0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #718096;
        transition: all 0.3s ease;
    }

    .step.active .step-icon {
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
    }

    .step span {
        font-size: 0.9rem;
        font-weight: 500;
        color: #718096;
    }

    .step.active span {
        color: #5260bf;
        font-weight: 600;
    }

    /* Form Card */
    .form-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .form-header {
        padding: 2rem 2rem 1rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .form-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .form-subtitle {
        color: #718096;
        font-size: 0.9rem;
        margin: 0;
    }

    .form-body {
        padding: 2rem;
    }

    /* Modern Form */
    .modern-form {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .form-group-modern {
        position: relative;
    }

    .input-group-modern {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        background: #f7fafc;
        border-radius: 15px;
        padding: 1.5rem;
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .input-group-modern:focus-within {
        border-color: #5260bf;
        background: white;
        box-shadow: 0 0 0 3px rgba(82, 96, 191, 0.1);
    }

    .input-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #5260bf, #667eea);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        flex-shrink: 0;
        margin-top: 0.5rem;
    }

    .input-icon i {
        font-size: 1.1rem;
    }

    .input-content {
        flex-grow: 1;
    }

    .modern-label {
        display: block;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .required-indicator {
        color: #e53e3e;
        margin-left: 0.25rem;
    }

    .form-control, .form-select {
        border: none;
        background: transparent;
        padding: 0.75rem 0;
        font-size: 1rem;
        color: #2d3748;
        border-radius: 0;
        border-bottom: 2px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-bottom-color: #5260bf;
        box-shadow: none;
        background: transparent;
    }

    .input-help {
        font-size: 0.85rem;
        color: #718096;
        margin-top: 0.5rem;
    }

    .input-error {
        margin-top: 0.5rem;
        padding: 0.5rem 1rem;
        background: #fed7d7;
        border-radius: 8px;
        border-left: 4px solid #e53e3e;
    }

    .input-error span {
        color: #c53030;
        font-size: 0.85rem;
        font-weight: 500;
    }

    /* File Upload Modern */
    .file-upload-modern {
        background: #f7fafc;
        border-radius: 15px;
        border: 2px dashed #cbd5e0;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .file-upload-modern:hover {
        border-color: #5260bf;
        background: #edf2f7;
    }

    .upload-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .upload-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #5260bf, #667eea);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .upload-icon i {
        font-size: 1.5rem;
    }

    .upload-content .modern-label {
        margin-bottom: 0.25rem;
    }

    .upload-subtitle {
        color: #718096;
        font-size: 0.9rem;
        margin: 0;
    }

    .upload-area {
        position: relative;
        padding: 2rem;
        text-align: center;
        min-height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .upload-area input[type="file"] {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        color: #718096;
        pointer-events: none;
    }

    .upload-placeholder i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .upload-placeholder span {
        font-weight: 500;
        font-size: 1rem;
    }

    .upload-placeholder small {
        font-size: 0.85rem;
        opacity: 0.8;
    }

    .upload-info {
        padding: 1rem 1.5rem;
        background: #edf2f7;
        border-top: 1px solid #e2e8f0;
    }

    /* Form Actions */
    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        padding-top: 2rem;
        border-top: 1px solid #e2e8f0;
    }

    .btn-modern {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 1rem;
    }

    .btn-modern.btn-primary {
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
    }

    .btn-modern.btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(82, 96, 191, 0.3);
        color: white;
    }

    .btn-modern.btn-secondary {
        background: #e2e8f0;
        color: #4a5568;
    }

    .btn-modern.btn-secondary:hover {
        background: #cbd5e0;
        color: #2d3748;
    }

    /* Form Errors */
    .form-errors {
        background: #fed7d7;
        border-radius: 12px;
        padding: 1rem;
        border-left: 4px solid #e53e3e;
        margin-bottom: 2rem;
    }

    .error-item {
        color: #c53030;
        font-weight: 500;
        display: flex;
        align-items: center;
    }

    /* Side Panel */
    .side-panel {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .tips-card, .file-types-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
    }

    .tips-header, .file-types-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        color: #5260bf;
    }

    .tips-header h4, .file-types-header h4 {
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
    }

    .tip-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
        font-size: 0.85rem;
    }

    .tip-item:last-child {
        margin-bottom: 0;
    }

    .tip-item i {
        color: #28a745;
        font-size: 0.9rem;
    }

    .file-type {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
        font-size: 0.85rem;
    }

    .file-type:last-child {
        margin-bottom: 0;
    }

    .file-type i {
        font-size: 1.1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-section {
            padding: 4rem 0 3rem 0;
        }

        .hero-title {
            font-size: 2rem;
        }

        .hero-subtitle {
            font-size: 1rem;
        }

        .floating-card {
            display: none;
        }

        .main-content {
            margin-top: -1rem;
            border-radius: 1rem 1rem 0 0;
        }

        .progress-steps {
            flex-direction: column;
            gap: 1rem;
        }

        .progress-steps::before {
            display: none;
        }

        .input-group-modern {
            flex-direction: column;
            gap: 1rem;
        }

        .input-icon {
            align-self: flex-start;
        }

        .form-actions {
            flex-direction: column;
            gap: 1rem;
        }

        .btn-modern {
            width: 100%;
            justify-content: center;
        }

        .side-panel {
            order: -1;
            margin-bottom: 2rem;
        }
    }

    @media (max-width: 576px) {
        .hero-section {
            padding: 3rem 0 2rem 0;
        }

        .hero-title {
            font-size: 1.75rem;
        }

        .form-progress-section {
            padding: 1.5rem;
        }

        .form-card {
            margin-bottom: 1rem;
        }

        .form-header, .form-body {
            padding: 1.5rem;
        }

        .input-group-modern {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // File upload enhancement
    const fileInput = document.querySelector('input[type="file"][name="file"]');
    const uploadArea = document.getElementById('uploadArea');
    const uploadInfo = document.getElementById('uploadInfo');
    const submitBtn = document.getElementById('submitBtn');

    // Image upload enhancement
    const imageInput = document.querySelector('input[type="file"][name="image"]');
    const imageUploadArea = document.getElementById('imageUploadArea');
    const imageUploadInfo = document.getElementById('imageUploadInfo');

    if (fileInput && uploadArea) {
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#5260bf';
            uploadArea.style.background = '#edf2f7';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#cbd5e0';
            uploadArea.style.background = '#f7fafc';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#cbd5e0';
            uploadArea.style.background = '#f7fafc';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                updateFileInfo(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                updateFileInfo(e.target.files[0]);
            }
        });

        function updateFileInfo(file) {
            const placeholder = uploadArea.querySelector('.upload-placeholder');
            if (placeholder) {
                placeholder.innerHTML = `
                    <i class="bi bi-file-earmark-check text-success"></i>
                    <span class="text-success">تم اختيار الملف</span>
                    <small>${file.name}</small>
                    <small class="text-muted">${formatFileSize(file.size)}</small>
                `;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    }

    // Image upload functionality
    if (imageInput && imageUploadArea) {
        // Drag and drop functionality for images
        imageUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            imageUploadArea.style.borderColor = '#5260bf';
            imageUploadArea.style.background = '#edf2f7';
        });

        imageUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            imageUploadArea.style.borderColor = '#cbd5e0';
            imageUploadArea.style.background = '#f7fafc';
        });

        imageUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            imageUploadArea.style.borderColor = '#cbd5e0';
            imageUploadArea.style.background = '#f7fafc';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                imageInput.files = files;
                updateImageInfo(files[0]);
            }
        });

        // Image input change
        imageInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                updateImageInfo(e.target.files[0]);
            }
        });

        function updateImageInfo(file) {
            const placeholder = imageUploadArea.querySelector('.upload-placeholder');
            if (placeholder) {
                // Check if it's an image file
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        placeholder.innerHTML = `
                            <div class="image-preview">
                                <img src="${e.target.result}" alt="معاينة الصورة" style="max-width: 100px; max-height: 100px; border-radius: 8px;">
                            </div>
                            <span class="text-success">تم اختيار الصورة</span>
                            <small>${file.name}</small>
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                        `;
                    };
                    reader.readAsDataURL(file);
                } else {
                    placeholder.innerHTML = `
                        <i class="bi bi-image-fill text-success"></i>
                        <span class="text-success">تم اختيار الملف</span>
                        <small>${file.name}</small>
                        <small class="text-muted">${formatFileSize(file.size)}</small>
                    `;
                }
            }
        }
    }

    // Form validation enhancement
    const form = document.getElementById('lectureForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                const formGroup = field.closest('.form-group-modern');
                if (formGroup) {
                    const errorDiv = formGroup.querySelector('.input-error');
                    if (errorDiv) {
                        errorDiv.remove();
                    }

                    if (!field.value.trim()) {
                        isValid = false;
                        showFieldError(formGroup, 'هذا الحقل مطلوب');
                    }
                }
            });

            if (!isValid) {
                e.preventDefault();
                scrollToFirstError();
            } else {
                // Show loading state
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
                    submitBtn.disabled = true;
                }
            }
        });
    }

    function showFieldError(formGroup, message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'input-error';
        errorDiv.innerHTML = `<span>${message}</span>`;
        formGroup.appendChild(errorDiv);
    }

    function scrollToFirstError() {
        const firstError = document.querySelector('.input-error');
        if (firstError) {
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    // Progress steps animation
    const steps = document.querySelectorAll('.step');
    let currentStep = 0;

    function updateProgress() {
        steps.forEach((step, index) => {
            if (index <= currentStep) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });
    }

    // Auto-advance steps based on form completion
    const courseField = document.querySelector('select[name="course"]');
    const titleField = document.querySelector('input[name="title"]');

    if (courseField) {
        courseField.addEventListener('change', function() {
            if (this.value) {
                currentStep = Math.max(currentStep, 0);
                updateProgress();
            }
        });
    }

    if (titleField) {
        titleField.addEventListener('input', function() {
            if (this.value.trim()) {
                currentStep = Math.max(currentStep, 1);
                updateProgress();
            }
        });
    }

    if (fileInput) {
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                currentStep = Math.max(currentStep, 2);
                updateProgress();
            }
        });
    }

    // Initialize progress
    updateProgress();

    // Smooth scrolling for form sections
    const formGroups = document.querySelectorAll('.form-group-modern');
    formGroups.forEach((group, index) => {
        const input = group.querySelector('input, select, textarea');
        if (input) {
            input.addEventListener('focus', function() {
                setTimeout(() => {
                    group.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);
            });
        }
    });

    // Additional form handling
    const form = document.getElementById('lectureForm');
    const submitBtn = document.getElementById('submitBtn');

    // Form submission handling
    if (form) {
        form.addEventListener('submit', function(e) {
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
            }
        });
    }

    // Course selection change
    const courseSelect = document.querySelector('select[name="course"]');
    if (courseSelect) {
        courseSelect.addEventListener('change', function() {
            if (this.value) {
                console.log('Course selected:', this.value);
            }
        });
    }
});
</script>
{% endblock %}
