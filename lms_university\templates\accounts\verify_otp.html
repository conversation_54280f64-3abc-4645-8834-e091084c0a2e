{% extends 'base.html' %}
{% load static %}
{% block title %}التحقق من الهوية{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/login_custom.css' %}">
<link rel="stylesheet" href="{% static 'css/verify_otp.css' %}">
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="login-icon">
                <i class="bi bi-shield-check"></i>
            </div>
            <h1 class="login-title">التحقق من الهوية</h1>
            <p class="login-subtitle">يرجى إدخال رمز التحقق المرسل</p>
        </div>
        <div class="login-body">
            <div class="otp-alert">
                <i class="bi bi-info-circle me-2"></i>
                {% if debug %}
                في بيئة التطوير: ستجد رمز التحقق في نافذة الكونسول (Terminal). الرجاء إدخال الرمز المكون من 6 أرقام.
                {% else %}
                تم إرسال رمز التحقق إلى بريدك الإلكتروني. الرجاء إدخال الرمز المكون من 6 أرقام.
                {% endif %}
            </div>
            <form method="post">
                {% csrf_token %}
                {% if debug %}
                <input type="hidden" name="debug_csrf" value="{{ csrf_token }}">
                {% endif %}
                <div class="mb-3">
                    <label for="{{ form.otp_code.id_for_label }}" class="otp-form-label">
                        <i class="bi bi-key me-1"></i>
                        {{ form.otp_code.label }}
                    </label>
                    <input type="text" name="otp_code" maxlength="6" class="otp-form-control" id="{{ form.otp_code.id_for_label }}" autocomplete="one-time-code" required>
                    {% if form.otp_code.errors %}
                        <div class="text-danger mt-1">
                            {% for error in form.otp_code.errors %}
                                <small>{{ error }}</small>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text otp-form-text">
                        <i class="bi bi-clock me-1"></i>
                        الرمز صالح لمدة 10 دقائق فقط
                    </div>
                </div>
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-login" id="otpBtn">
                        <i class="bi bi-check-circle me-2"></i>
                        تأكيد الرمز
                        <div class="loading-spinner" id="otpLoadingSpinner"></div>
                    </button>
                    <a href="{% url 'accounts:resend_otp' %}" class="btn btn-outline-secondary btn-login">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        إعادة إرسال الرمز
                    </a>
                    <a href="{% url 'accounts:login' %}" class="btn btn-outline-primary btn-login">
                        <i class="bi bi-arrow-left me-2"></i>
                        العودة لتسجيل الدخول
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/verify_otp.js' %}"></script>
{% endblock %}
