/* Lecturer Dashboard Styles with Cairo Font */

/* Global Cairo Font Application for Lecturer Dashboard */
* {
    font-family: 'Cairo', sans-serif !important;
}

body {
    padding-top: 0 !important;
    font-family: 'Cairo', sans-serif !important;
    font-weight: 400;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #5260bf 0%, #764ba2 100%);
    color: white;
    padding: 6rem 0 4rem 0;
    margin-top: 0;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

/* Main Content */
.main-content {
    position: relative;
    z-index: 2;
    background: #f8f9fa;
    margin-top: -2rem;
    border-radius: 2rem 2rem 0 0;
}

.min-vh-50 {
    min-height: 50vh;
}

/* Hero Content */
.hero-content {
    position: relative;
    z-index: 2;
}

.welcome-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    backdrop-filter: blur(10px);
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
    font-family: 'Cairo', sans-serif !important;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
    font-family: 'Cairo', sans-serif !important;
    font-weight: 400;
}

.hero-actions .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif !important;
}

.hero-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

/* Floating Cards Animation */
.hero-illustration {
    position: relative;
    height: 200px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    gap: 1rem;
}

.floating-card {
    position: relative;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    text-align: center;
    animation: float 6s ease-in-out infinite;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif !important;
    flex: 1;
    max-width: 120px;
    margin: 0 5px;
}

.floating-card:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.25);
}

.floating-card:nth-child(1) {
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    animation-delay: 4s;
}

.floating-card i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.floating-card span {
    font-size: 1.5rem;
    font-weight: bold;
    display: block;
    font-family: 'Cairo', sans-serif !important;
}

.floating-card small {
    opacity: 0.8;
    font-family: 'Cairo', sans-serif !important;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Modern Stats Cards */
.stats-card-modern {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.stats-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #5260bf, #764ba2);
}

.stats-card-modern:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.stats-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.stats-icon-modern {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.stats-icon-modern i {
    font-size: 1.5rem;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #5260bf, #667eea);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.stats-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif !important;
}

.stats-number-modern {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    color: #2d3748;
    font-family: 'Cairo', sans-serif !important;
}

.stats-label-modern {
    color: #718096;
    font-weight: 500;
    margin-bottom: 1rem;
    font-family: 'Cairo', sans-serif !important;
}

.stats-progress {
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 3px;
    transition: width 1s ease;
}

/* Typography for all elements */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600;
}

p, span, div, a, label {
    font-family: 'Cairo', sans-serif !important;
}

.btn {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500;
}

.card-title {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600;
}

.nav-link {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500;
}

/* Form elements */
.form-control, .form-select, .form-check-label {
    font-family: 'Cairo', sans-serif !important;
}

/* Alerts and badges */
.alert {
    font-family: 'Cairo', sans-serif !important;
}

.badge {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500;
}

/* Tables */
.table {
    font-family: 'Cairo', sans-serif !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 4rem 0 3rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-actions .btn {
        display: block;
        margin-bottom: 1rem;
        width: 100%;
    }
    
    .hero-illustration {
        height: 150px;
        padding: 0 10px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .floating-card {
        width: 90px;
        padding: 1rem;
        margin: 5px;
    }

    .floating-card i {
        font-size: 1.2rem;
    }

    .floating-card span {
        font-size: 1.1rem;
    }

    .floating-card small {
        font-size: 0.7rem;
    }
    
    .main-content {
        margin-top: -1rem;
        border-radius: 1rem 1rem 0 0;
    }
    
    .stats-number-modern {
        font-size: 2rem;
    }
    
    .min-vh-50 {
        min-height: auto;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 3rem 0 2rem 0;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .hero-actions .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .stats-card-modern {
        padding: 1.5rem;
    }
}

/* Quick Actions Section */
.quick-actions-section {
    margin-bottom: 3rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', sans-serif !important;
}

.section-subtitle {
    color: #718096;
    font-size: 1.1rem;
    font-family: 'Cairo', sans-serif !important;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.quick-action-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.quick-action-card.primary::before {
    background: linear-gradient(90deg, #5260bf, #667eea);
}

.quick-action-card.success::before {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.quick-action-card.info::before {
    background: linear-gradient(90deg, #17a2b8, #6f42c1);
}

.quick-action-card.warning::before {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.quick-action-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.action-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
}

.primary .action-icon {
    background: linear-gradient(135deg, #5260bf, #667eea);
}

.success .action-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.info .action-icon {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.warning .action-icon {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.action-icon i {
    font-size: 2rem;
}

.action-content h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', sans-serif !important;
}

.action-content p {
    color: #718096;
    margin-bottom: 1.5rem;
    line-height: 1.6;
    font-family: 'Cairo', sans-serif !important;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    color: #5260bf;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif !important;
}

.action-btn:hover {
    color: #764ba2;
    transform: translateX(-5px);
}

/* Content Cards */
.content-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
    overflow: hidden;
    height: 100%;
}

.content-header {
    padding: 2rem 2rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 1px solid #e2e8f0;
}

.content-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
    font-family: 'Cairo', sans-serif !important;
}

.content-subtitle {
    color: #718096;
    font-size: 0.9rem;
    font-family: 'Cairo', sans-serif !important;
}

.view-all-btn {
    display: inline-flex;
    align-items: center;
    color: #5260bf;
    font-weight: 600;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif !important;
}

.view-all-btn:hover {
    background: #f7fafc;
    color: #764ba2;
}

.content-body {
    padding: 2rem;
}

/* Lectures Timeline */
.lectures-timeline {
    position: relative;
}

.timeline-item {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 2rem;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0.5rem;
    width: 12px;
    height: 12px;
    background: #5260bf;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #e2e8f0;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 1.5rem;
    bottom: -1rem;
    width: 2px;
    background: #e2e8f0;
}

.timeline-item:last-child::before {
    display: none;
}

.lecture-card-mini {
    background: #f7fafc;
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.lecture-card-mini:hover {
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateX(5px);
}

.lecture-mini-image {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
}

.lecture-mini-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.lecture-mini-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #5260bf, #667eea);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.lecture-info {
    flex: 1;
    min-width: 0;
}

.lecture-info {
    margin-bottom: 1rem;
}

.lecture-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', sans-serif !important;
}

.lecture-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.course-badge {
    background: #5260bf;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    font-family: 'Cairo', sans-serif !important;
}

.time-badge {
    color: #718096;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    font-family: 'Cairo', sans-serif !important;
}

.lecture-desc {
    color: #718096;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
    font-family: 'Cairo', sans-serif !important;
}

.lecture-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn-mini {
    width: 35px;
    height: 35px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.action-btn-mini.edit {
    background: #e3f2fd;
    color: #1976d2;
}

.action-btn-mini.download {
    background: #e8f5e8;
    color: #388e3c;
}

.action-btn-mini.view {
    background: #f3e5f5;
    color: #7b1fa2;
}

.action-btn-mini:hover {
    transform: scale(1.1);
}

/* Courses Grid */
.courses-grid {
    display: grid;
    gap: 1.5rem;
}

.course-card-mini {
    background: #f7fafc;
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.course-card-mini:hover {
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-5px);
}

.course-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.course-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #5260bf, #667eea);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.course-icon i {
    font-size: 1.25rem;
}

.course-status.active {
    color: #28a745;
    font-size: 1.25rem;
}

.course-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
    font-family: 'Cairo', sans-serif !important;
}

.course-code {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-family: 'Cairo', sans-serif !important;
}

.course-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #718096;
    font-size: 0.85rem;
    font-family: 'Cairo', sans-serif !important;
}

.course-actions {
    display: flex;
    gap: 0.5rem;
}

.course-action-btn {
    width: 35px;
    height: 35px;
    border-radius: 10px;
    background: #5260bf;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.course-action-btn:hover {
    background: #764ba2;
    transform: scale(1.1);
    color: white;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: #f7fafc;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: #718096;
}

.empty-icon i {
    font-size: 2rem;
}

.empty-state h4 {
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', sans-serif !important;
}

.empty-state p {
    color: #718096;
    margin-bottom: 2rem;
    font-family: 'Cairo', sans-serif !important;
}

/* Additional responsive design for small screens */
@media (max-width: 576px) {
    .hero-section {
        padding: 3rem 0 2rem 0;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .hero-illustration {
        height: 120px;
        padding: 0 5px;
        flex-direction: row;
        justify-content: space-around;
    }

    .floating-card {
        width: 80px;
        padding: 0.75rem;
        margin: 2px;
    }

    .floating-card i {
        font-size: 1rem;
    }

    .floating-card span {
        font-size: 1rem;
    }

    .floating-card small {
        font-size: 0.6rem;
    }
}
