/* Profile Page Modern Styles */
* {
    font-family: 'Cairo', sans-serif;
}

body {
    background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 50%, #8b5cf6 100%) !important;
    min-height: 100vh !important;
    padding-bottom: 0 !important;
}

.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    min-height: auto;
}

.profile-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-top: 1rem;
}

.profile-left-section,
.profile-right-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.profile-header {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 20px 20px 0 0;
    padding: 2rem 1.5rem 1.5rem;
    margin-bottom: 0;
    box-shadow: 0 8px 32px rgba(79, 70, 229, 0.3);
    text-align: center;
    border: none;
    color: white;
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.profile-avatar-section {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
    z-index: 2;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.9);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    object-fit: cover;
    transition: all 0.3s ease;
}

.profile-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.avatar-placeholder {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #06b6d4, #8b5cf6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    border: 4px solid rgba(255, 255, 255, 0.9);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.profile-info h1 {
    font-size: 1.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 2;
    position: relative;
}

.profile-email {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.75rem;
    z-index: 2;
    position: relative;
}

.profile-id {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    z-index: 2;
    position: relative;
}

.id-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
}

.id-number {
    font-size: 0.95rem;
    color: white;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.15);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.profile-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    color: white;
    padding: 0.5rem 1.2rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 2;
    position: relative;
}

.back-button {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    color: #4f46e5;
    border: 2px solid rgba(255, 255, 255, 0.8);
    padding: 0.7rem 1.5rem;
    border-radius: 12px;
    font-weight: 700;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 10;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
    background: white;
    color: #4f46e5;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.profile-form-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 0 0 15px 15px;
    padding: 1.25rem;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
    border: none;
    margin-bottom: 1rem;
}

.form-section-title {
    font-size: 1.2rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
    justify-content: center;
}

.form-section-title i {
    color: #4f46e5;
    font-size: 1.25rem;
}

.form-group-modern {
    margin-bottom: 1.25rem;
}

.form-label-modern {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-control-modern {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: #f8fafc;
    color: #1e293b;
}

.form-control-modern:focus {
    outline: none;
    border-color: #4f46e5;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    transform: translateY(-1px);
}

.form-control-modern:hover {
    border-color: #cbd5e0;
    background: white;
}

.form-control-modern:disabled {
    background: #edf2f7;
    color: #718096;
    cursor: not-allowed;
}

.file-upload-modern {
    position: relative;
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    padding: 1.25rem;
    text-align: center;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.file-upload-modern:hover {
    border-color: #5260bf;
    background: #edf2f7;
}

.file-upload-modern input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.upload-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #4f46e5, #06b6d4);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    color: white;
    font-size: 1.25rem;
}

.upload-text {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.upload-hint {
    color: #6b7280;
    font-size: 0.8rem;
}

.btn-save-modern {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 700;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: block;
    width: 100%;
    text-align: center;
    cursor: pointer;
    margin-bottom: 0.75rem;
}

.btn-save-modern:hover {
    background: linear-gradient(135deg, #0891b2, #0e7490);
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(6, 182, 212, 0.4);
    color: white;
}

.btn-password-modern {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 700;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: block;
    width: 100%;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
}

.btn-password-modern:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
    color: white;
}

.password-section {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    padding: 1.25rem;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
    border: none;
    text-align: center;
    margin-bottom: 1rem;
}

.password-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    color: white;
    font-size: 1.25rem;
}

.password-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.password-description {
    color: #64748b;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    line-height: 1.4;
}

.alert-modern {
    background: rgba(72, 187, 120, 0.1);
    border: 1px solid rgba(72, 187, 120, 0.2);
    color: #2f855a;
    padding: 1rem 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-modern.alert-error {
    background: rgba(245, 101, 101, 0.1);
    border: 1px solid rgba(245, 101, 101, 0.2);
    color: #c53030;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.error-text {
    color: #e53e3e;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    font-weight: 500;
}

.form-control-error {
    border-color: #e53e3e;
    background: rgba(245, 101, 101, 0.05);
}

@media (max-width: 768px) {
    .profile-container {
        padding: 0 0.5rem;
    }

    .profile-content-wrapper {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .profile-header,
    .profile-form-card,
    .password-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .profile-info h1 {
        font-size: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .btn-save-modern,
    .btn-password-modern {
        width: 100%;
        justify-content: center;
    }
}
