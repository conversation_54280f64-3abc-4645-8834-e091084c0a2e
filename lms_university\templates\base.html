{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة المحتوى التعليمي{% endblock %}</title>

    <!-- Google Fonts - Cairo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- External CSS Files -->
    {% block extra_css %}{% endblock %}
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
</head>
<body>
    <!-- Navigation -->
    {% if user.is_authenticated and request.resolver_match.url_name != 'login' and request.resolver_match.url_name != 'verify_otp' %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'core:dashboard' %}">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة المحتوى التعليمي
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" href="{% url 'core:dashboard' %}">
                            <i class="bi bi-house-fill align-middle me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'lectures' in request.resolver_match.namespace %}active{% endif %}" href="{% url 'lectures:lecture_list' %}">
                            <i class="bi bi-journal-text align-middle ms-2"></i>المحاضرات
                        </a>
                    </li>
                    {% if user.user_type == 'lecturer' %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'lecturer_dashboard' %}active{% endif %}" href="{% url 'lectures:lecturer_dashboard' %}">
                            <i class="bi bi-person-workspace align-middle ms-2"></i>لوحة المدرس
                        </a>
                    </li>
                    {% endif %}
                    {% if user.user_type == 'admin' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin:index' %}">
                            <i class="bi bi-gear-fill me-1"></i>الإدارة
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            {% if user.photo %}
                       <img src="{{ user.photo.url }}" alt="Avatar" class="rounded-circle " width="32" height="32">
                         {% else %}
                          <i class="bi bi-person-circle me-3 fs-4"></i>
                         {% endif %}
                         <span class="me-3">{{ user.full_name }}</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item d-flex justify-content-between align-items-center" href="{% url 'accounts:profile' %}">
                                    <span class="ms-2">تعديل الملف الشخصي</span>
                                    <i class="bi bi-person-lines-fill"></i>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex justify-content-between align-items-center" href="{% url 'accounts:password_change' %}">
                                    <span class="ms-2">تغيير كلمة المرور</span>
                                    <i class="bi bi-key"></i>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item d-flex justify-content-between align-items-center" href="{% url 'accounts:logout' %}">
                                    <span class="ms-2">تسجيل الخروج</span>
                                    <i class="bi bi-box-arrow-right"></i>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% elif request.resolver_match.url_name != 'login' and request.resolver_match.url_name != 'verify_otp' %}
    <!-- Simple header for non-authenticated users (except login page) -->
    <nav class="navbar navbar-dark navbar-gradient">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة المحتوى التعليمي
            </a>
        </div>
    </nav>
    {% endif %}

    <!-- Messages -->
    {% if messages and request.resolver_match.url_name != 'login' %}
    <div class="container mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main>
        {% block content %}
        {% endblock %}
    </main>

    <!-- Footer -->
    {% if request.resolver_match.url_name != 'login' and request.resolver_match.url_name != 'verify_otp' %}
        {% include 'includes/footer.html' %}
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    {% block extra_js %}{% endblock %}
</body>
</html>
