{% extends 'base.html' %}
{% load static %}

{% block title %}محاضراتي{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/lecturer_lectures.css' %}">
<link rel="stylesheet" href="{% static 'css/custom_buttons.css' %}">
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="hero-content">
                    <div class="welcome-badge">
                        <i class="bi bi-collection-fill me-2"></i>
                        إدارة المحاضرات
                    </div>
                    <h1 class="hero-title">
                        محاضراتي
                    </h1>
                    <p class="hero-subtitle">
                        إدارة وتنظيم جميع محاضراتك في مكان واحد
                    </p>
                    <div class="hero-actions">
                        <a href="{% url 'lectures:add_lecture' %}" class="btn btn-primary btn-lg me-3">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة محاضرة جديدة
                        </a>
                        <a href="{% url 'lectures:lecturer_dashboard' %}" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-arrow-left me-2"></i>
                            لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="bi bi-journal-text"></i>
                        </div>
                        <div class="stat-content">
                            <h3>{{ page_obj.paginator.count|default:0 }}</h3>
                            <p>إجمالي المحاضرات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="bi bi-book"></i>
                        </div>
                        <div class="stat-content">
                            <h3>{{ assigned_courses|length|default:0 }}</h3>
                            <p>المقررات النشطة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="main-content">
<div class="container py-5">

    <!-- Enhanced Search and Filter Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="search-filter-card">
                <div class="search-header">
                    <div class="header-info">
                        <h3 class="search-title">
                            <i class="bi bi-funnel me-2"></i>
                            البحث والفلترة
                        </h3>
                        <p class="search-subtitle">ابحث وفلتر محاضراتك بسهولة</p>
                    </div>
                    {% if search_query or course_filter %}
                    <a href="{% url 'lectures:lecturer_lectures' %}" class="clear-filters-btn">
                        <i class="bi bi-x-circle me-2"></i>
                        مسح الفلاتر
                    </a>
                    {% endif %}
                </div>

                <div class="search-body">
                    <form method="get" class="search-form">
                        <div class="search-grid">
                            <!-- Search Input -->
                            <div class="search-group">
                                <div class="search-input-wrapper">
                                    <div class="search-icon">
                                        <i class="bi bi-search"></i>
                                    </div>
                                    <input type="text" class="search-input" id="search" name="search"
                                           value="{{ search_query }}" placeholder="ابحث في عناوين المحاضرات والوصف...">
                                </div>
                            </div>

                            <!-- Course Filter -->
                            <div class="filter-group">
                                <div class="filter-wrapper">
                                    <div class="filter-icon">
                                        <i class="bi bi-book"></i>
                                    </div>
                                    <select class="filter-select" id="course" name="course">
                                        <option value="">جميع المقررات</option>
                                        {% for course in assigned_courses %}
                                        <option value="{{ course.id }}"
                                                {% if course_filter == course.id|stringformat:"s" %}selected{% endif %}>
                                            {{ course.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- Search Button -->
                            <div class="search-action">
                                <button type="submit" class="search-btn">
                                    <i class="bi bi-search me-2"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Lectures Grid -->
    <div class="row">
        <div class="col-12">
            {% if page_obj %}
                <div class="lectures-section">
                    <div class="lectures-header">
                        <div class="header-info">
                            <h3 class="lectures-title">
                                <i class="bi bi-collection me-2"></i>
                                المحاضرات
                            </h3>
                            <p class="lectures-subtitle">{{ page_obj.paginator.count }} محاضرة متاحة</p>
                        </div>
                        <div class="view-options">
                            <div class="view-toggle">
                                <button class="view-btn active" data-view="grid" title="عرض شبكي">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                </button>
                                <button class="view-btn" data-view="list" title="عرض قائمة">
                                    <i class="bi bi-list-ul"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Grid View -->
                    <div class="lectures-grid" id="gridView">
                        {% for lecture in page_obj %}
                        <div class="lecture-card">
                            <div class="lecture-card-header">
                                <div class="lecture-icon">
                                    <i class="bi bi-play-circle-fill"></i>
                                </div>
                                <div class="lecture-date">
                                    {{ lecture.date|date:"d/m" }}
                                </div>
                            </div>

                            <div class="lecture-card-body">
                                <h4 class="lecture-title">{{ lecture.title }}</h4>
                                {% if lecture.description %}
                                <p class="lecture-description">{{ lecture.description|truncatechars:80 }}</p>
                                {% endif %}

                                <div class="lecture-meta">
                                    <div class="course-badge">
                                        <i class="bi bi-book me-1"></i>
                                        {{ lecture.course.name }}
                                    </div>
                                    <div class="time-badge">
                                        <i class="bi bi-clock me-1"></i>
                                        {{ lecture.date|date:"H:i" }}
                                    </div>
                                </div>
                            </div>

                            <div class="lecture-card-footer">
                                <div class="file-status">
                                    {% if lecture.file %}
                                    <span class="file-available">
                                        <i class="bi bi-file-earmark-check me-1"></i>
                                        ملف متاح
                                    </span>
                                    {% else %}
                                    <span class="file-missing">
                                        <i class="bi bi-file-earmark-x me-1"></i>
                                        لا يوجد ملف
                                    </span>
                                    {% endif %}
                                </div>

                                <div class="lecture-actions">
                                    {% if lecture.file %}
                                    <a href="{{ lecture.file.url }}" target="_blank"
                                       class="action-btn download" title="تحميل">
                                        <i class="bi bi-download"></i>
                                    </a>
                                    {% endif %}
                                    <a href="{% url 'lectures:edit_lecture' lecture.id %}"
                                       class="action-btn edit" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="{% url 'lectures:update_lecture_image' lecture.id %}"
                                       class="action-btn image" title="إدارة الصورة">
                                        <i class="bi bi-image"></i>
                                    </a>
                                    <button type="button" class="action-btn duplicate"
                                            onclick="duplicateLecture({{ lecture.id }})" title="تكرار">
                                        <i class="bi bi-files"></i>
                                    </button>
                                    <a href="{% url 'lectures:delete_lecture' lecture.id %}"
                                       class="action-btn delete" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- List View -->
                    <div class="lectures-list" id="listView" style="display: none;">
                        {% for lecture in page_obj %}
                        <div class="lecture-list-item">
                            <div class="lecture-list-icon">
                                <i class="bi bi-play-circle-fill"></i>
                            </div>

                            <div class="lecture-list-content">
                                <div class="lecture-list-header">
                                    <h4 class="lecture-list-title">{{ lecture.title }}</h4>
                                    <div class="lecture-list-date">{{ lecture.date|date:"d/m/Y H:i" }}</div>
                                </div>

                                {% if lecture.description %}
                                <p class="lecture-list-description">{{ lecture.description|truncatechars:100 }}</p>
                                {% endif %}

                                <div class="lecture-list-meta">
                                    <span class="course-tag">{{ lecture.course.name }}</span>
                                    {% if lecture.file %}
                                    <span class="file-tag available">ملف متاح</span>
                                    {% else %}
                                    <span class="file-tag missing">لا يوجد ملف</span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="lecture-list-actions">
                                {% if lecture.file %}
                                <a href="{{ lecture.file.url }}" target="_blank"
                                   class="list-action-btn download" title="تحميل">
                                    <i class="bi bi-download"></i>
                                </a>
                                {% endif %}
                                <a href="{% url 'lectures:edit_lecture' lecture.id %}"
                                   class="list-action-btn edit" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'lectures:update_lecture_image' lecture.id %}"
                                   class="list-action-btn image" title="إدارة الصورة">
                                    <i class="bi bi-image"></i>
                                </a>
                                <button type="button" class="list-action-btn duplicate"
                                        onclick="duplicateLecture({{ lecture.id }})" title="تكرار">
                                    <i class="bi bi-files"></i>
                                </button>
                                <a href="{% url 'lectures:delete_lecture' lecture.id %}"
                                   class="list-action-btn delete" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Enhanced Pagination -->
                {% if page_obj.has_other_pages %}
                <div class="pagination-section">
                    <div class="pagination-info">
                        <span class="pagination-text">
                            عرض {{ page_obj.start_index }}-{{ page_obj.end_index }} من {{ page_obj.paginator.count }} محاضرة
                        </span>
                    </div>

                    <nav aria-label="تصفح المحاضرات">
                        <ul class="pagination-modern">
                            {% if page_obj.has_previous %}
                            <li class="pagination-item">
                                <a class="pagination-link prev" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if course_filter %}&course={{ course_filter }}{% endif %}">
                                    <i class="bi bi-chevron-right me-1"></i>
                                    السابق
                                </a>
                            </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <li class="pagination-item active">
                                <span class="pagination-link">{{ num }}</span>
                            </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="pagination-item">
                                <a class="pagination-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if course_filter %}&course={{ course_filter }}{% endif %}">
                                    {{ num }}
                                </a>
                            </li>
                            {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                            <li class="pagination-item">
                                <a class="pagination-link next" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if course_filter %}&course={{ course_filter }}{% endif %}">
                                    التالي
                                    <i class="bi bi-chevron-left ms-1"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}

            {% else %}
                <!-- Enhanced Empty State -->
                <div class="empty-state-section">
                    <div class="empty-state-content">
                        <div class="empty-state-icon">
                            {% if search_query or course_filter %}
                            <i class="bi bi-search"></i>
                            {% else %}
                            <i class="bi bi-journal-plus"></i>
                            {% endif %}
                        </div>

                        <h3 class="empty-state-title">
                            {% if search_query or course_filter %}
                            لا توجد نتائج
                            {% else %}
                            لا توجد محاضرات بعد
                            {% endif %}
                        </h3>

                        <p class="empty-state-description">
                            {% if search_query or course_filter %}
                            لم يتم العثور على محاضرات تطابق معايير البحث المحددة
                            {% else %}
                            ابدأ رحلتك التعليمية بإضافة أول محاضرة لك
                            {% endif %}
                        </p>

                        <div class="empty-state-actions">
                            {% if search_query or course_filter %}
                            <a href="{% url 'lectures:lecturer_lectures' %}" class="btn-modern btn-secondary">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                مسح الفلاتر
                            </a>
                            {% endif %}
                            <a href="{% url 'lectures:add_lecture' %}" class="btn-modern btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>
                                إضافة محاضرة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
</div>

<!-- CSRF Token for AJAX -->
<input type="hidden" id="csrf-token" value="{{ csrf_token }}">
{% endblock %}



{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View toggle functionality
    const viewButtons = document.querySelectorAll('.view-btn');
    const gridView = document.getElementById('gridView');
    const listView = document.getElementById('listView');

    viewButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const view = this.dataset.view;

            // Update active button
            viewButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // Toggle views
            if (view === 'grid') {
                gridView.style.display = 'grid';
                listView.style.display = 'none';
            } else {
                gridView.style.display = 'none';
                listView.style.display = 'block';
            }

            // Save preference
            localStorage.setItem('lecturesView', view);
        });
    });

    // Load saved view preference
    const savedView = localStorage.getItem('lecturesView') || 'grid';
    const savedViewBtn = document.querySelector(`[data-view="${savedView}"]`);
    if (savedViewBtn) {
        savedViewBtn.click();
    }

    // Auto-submit form on filter change
    const courseFilter = document.getElementById('course');
    if (courseFilter) {
        courseFilter.addEventListener('change', function() {
            this.form.submit();
        });
    }

    // Search input enhancement
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // Auto-submit after 1 second of no typing
                if (this.value.length >= 3 || this.value.length === 0) {
                    this.form.submit();
                }
            }, 1000);
        });
    }
});

function duplicateLecture(lectureId) {
    if (confirm('هل تريد تكرار هذه المحاضرة؟')) {
        const csrfToken = document.getElementById('csrf-token').value;

        // Show loading state
        const duplicateBtn = document.querySelector(`[onclick="duplicateLecture(${lectureId})"]`);
        const originalContent = duplicateBtn.innerHTML;
        duplicateBtn.innerHTML = '<i class="bi bi-hourglass-split"></i>';
        duplicateBtn.disabled = true;

        fetch(`/lectures/lecturer/duplicate/${lectureId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken,
                'Content-Type': 'application/json',
            },
        })
        .then(response => {
            if (response.ok) {
                // Show success message
                showNotification('تم تكرار المحاضرة بنجاح!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                throw new Error('فشل في تكرار المحاضرة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء تكرار المحاضرة', 'error');
            duplicateBtn.innerHTML = originalContent;
            duplicateBtn.disabled = false;
        });
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>
{% endblock %}
