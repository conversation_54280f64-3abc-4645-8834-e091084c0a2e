/* Global Cairo Font Application */
* {
    font-family: 'Cairo', sans-serif !important;
}

body, .verify-otp-container, .verify-otp-title, .verify-otp-subtitle, .verify-otp-body, .otp-form-control, .otp-btn, h1, h2, h3, h4, h5, h6, p, span, div, a, input, button {
    font-family: 'Cairo', sans-serif !important;
}

/* Typography Weights */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
}

.btn, .otp-btn {
    font-weight: 500;
}
.verify-otp-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5dd5d 0%, #fffbe6 100%);
    animation: fadeIn 1s;
}
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
.verify-otp-card {
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(245, 221, 93, 0.18), 0 1.5px 8px rgba(0,0,0,0.07);
    padding: 2.5rem 2.2rem 2rem 2.2rem;
    max-width: 400px;
    width: 100%;
    animation: slideUp 0.8s cubic-bezier(.77,0,.18,1);
}
@keyframes slideUp {
    from { transform: translateY(40px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
.verify-otp-header {
    text-align: center;
    margin-bottom: 1.5rem;
}
.verify-otp-icon {
    background: #5dc97a;
    border-radius: 50%;
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    box-shadow: 0 2px 8px rgba(93, 201, 122, 0.18);
    font-size: 2.2rem;
    color: #fff;
    border: 2px solid #e6fff2;
}
.verify-otp-title {
    font-weight: 900;
    font-size: 2.1rem;
    color: #222;
    letter-spacing: 0.5px;
}
.verify-otp-subtitle {
    color: #5dc97a;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}
.verify-otp-body {
    margin-top: 1.2rem;
}
.otp-form-label {
    font-weight: 700;
    color: #222;
    margin-bottom: 0.2rem;
}
.otp-form-text {
    color: #5dc97a;
    margin-bottom: 1.2rem;
    font-size: 1rem;
}
.otp-form-control {
    border-radius: 10px;
    border: 1.5px solid #f5dd5d;
    background: #fffbe6;
    font-size: 1.2rem;
    color: #222;
    text-align: center;
    letter-spacing: 0.5rem;
    font-weight: bold;
    transition: border-color 0.2s;
}
.otp-form-control:focus {
    border-color: #5dc97a;
    box-shadow: 0 0 0 0.15rem rgba(93, 201, 122, 0.18);
    background: #f6fff9;
}
.otp-btn, .btn-login {
    border-radius: 10px;
    font-weight: 900;
    font-size: 1.1rem;
    width: 100%;
    margin-bottom: 0.5rem;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.otp-btn-success, .btn-login {
    background: linear-gradient(90deg, #5dc97a 60%, #b2ffd6 100%);
    color: #222;
    border: none;
}
.otp-btn-success:hover, .otp-btn-success:focus, .btn-login:hover, .btn-login:focus {
    background: linear-gradient(90deg, #b2ffd6 0%, #5dc97a 100%);
    color: #fff;
    box-shadow: 0 4px 16px rgba(93, 201, 122, 0.18);
}
.otp-btn-outline, .btn-outline-secondary, .btn-outline-primary {
    border-radius: 10px;
}
.otp-alert {
    color: #5dc97a;
    background: #e6fff2;
    border-radius: 8px;
    padding: 0.5rem 0.7rem;
    box-shadow: 0 1px 4px rgba(93, 201, 122, 0.08);
    text-align: center;
    margin-bottom: 1.2rem;
}
.text-danger, .alert-danger {
    color: #d9534f !important;
    font-size: 0.97rem;
    margin-top: 0.2rem;
    margin-bottom: 0.2rem;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

/* زر تأكيد الرمز */
.btn-login {
    width: 100%;
    background: linear-gradient(90deg, #4f8cff 0%, #17439c 100%);
    color: #fff;
    font-weight: 900;
    border: none;
    border-radius: 10px;
    padding: 0.7rem 0;
    font-size: 1.15rem;
    margin-top: 0.5rem;
    margin-bottom: 0.7rem;
    box-shadow: 0 2px 8px rgba(55, 85, 231, 0.10);
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    position: relative;
    overflow: hidden;
}
.btn-login:hover, .btn-login:focus {
    background: linear-gradient(90deg, #17439c 0%, #4f8cff 100%);
    color: #fff;
}

/* أزرار الثانوي */
.btn-outline-secondary.btn-login,
.btn-outline-primary.btn-login {
    border-radius: 10px;
    font-weight: 700;
    font-size: 1.05rem;
    padding: 0.7rem 0;
    margin-bottom: 0.5rem;
    transition: background 0.2s, color 0.2s, border-color 0.2s;
}

.btn-outline-secondary.btn-login {
    border: 2px solid #4f8cff;
    color: #4f8cff;
    background: #fff;
}
.btn-outline-secondary.btn-login:hover,
.btn-outline-secondary.btn-login:focus {
    background: #eaf3ff;
    color: #17439c;
    border-color: #17439c;
}

.btn-outline-primary.btn-login {
    border: 2px solid #17439c;
    color: #17439c;
    background: #fff;
}
.btn-outline-primary.btn-login:hover,
.btn-outline-primary.btn-login:focus {
    background: #eaf3ff;
    color: #17439c;
    border-color: #4f8cff;
}
