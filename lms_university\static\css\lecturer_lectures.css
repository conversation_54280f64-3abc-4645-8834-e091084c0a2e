/* Lecturer Lectures Page Styles */

/* Global Styles */
body {
    padding-top: 0 !important;
    font-family: 'Cairo', Arial, Tahoma, sans-serif !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #5260bf 0%, #764ba2 100%);
    color: white;
    padding: 6rem 0 4rem 0;
    margin-top: 0;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

/* Main Content */
.main-content {
    position: relative;
    z-index: 2;
    background: #f8f9fa;
    margin-top: -2rem;
    border-radius: 2rem 2rem 0 0;
}

/* Hero Content */
.hero-content {
    position: relative;
    z-index: 2;
}

.welcome-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    backdrop-filter: blur(10px);
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-actions .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.hero-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

/* Hero Stats */
.hero-stats {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    justify-content: center;
    height: 100%;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.25);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-content p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

/* Search Filter Card */
.search-filter-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
    overflow: hidden;
}

.search-header {
    padding: 2rem 2rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 1px solid #e2e8f0;
}

.search-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.search-subtitle {
    color: #718096;
    font-size: 0.9rem;
    margin: 0;
}

.clear-filters-btn {
    display: inline-flex;
    align-items: center;
    color: #e53e3e;
    font-weight: 600;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.clear-filters-btn:hover {
    background: #fed7d7;
    color: #c53030;
}

.search-body {
    padding: 2rem;
}

.search-grid {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1.5rem;
    align-items: end;
}

.search-input-wrapper {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #718096;
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.search-input:focus {
    border-color: #5260bf;
    background: white;
    box-shadow: 0 0 0 3px rgba(82, 96, 191, 0.1);
    outline: none;
}

.filter-wrapper {
    position: relative;
    min-width: 200px;
}

.filter-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #718096;
    z-index: 2;
}

.filter-select {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f7fafc;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
}

.filter-select:focus {
    border-color: #5260bf;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(82, 96, 191, 0.1);
    outline: none;
}

.search-btn {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #5260bf, #667eea);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(82, 96, 191, 0.3);
}

/* Lectures Section */
.lectures-section {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
    overflow: hidden;
}

.lectures-header {
    padding: 2rem 2rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 1px solid #e2e8f0;
}

.lectures-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.lectures-subtitle {
    color: #718096;
    font-size: 0.9rem;
    margin: 0;
}

.view-options {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.view-toggle {
    display: flex;
    background: #f7fafc;
    border-radius: 10px;
    padding: 0.25rem;
}

.view-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    background: transparent;
    border-radius: 8px;
    color: #718096;
    transition: all 0.3s ease;
    cursor: pointer;
}

.view-btn.active {
    background: #5260bf;
    color: white;
}

.view-btn:hover:not(.active) {
    background: #e2e8f0;
    color: #2d3748;
}

/* Lectures Grid */
.lectures-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    padding: 2rem;
}

.lecture-card {
    background: #f7fafc;
    border-radius: 15px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    overflow: hidden;
}

.lecture-card:hover {
    background: white;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    transform: translateY(-5px);
}

.lecture-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 1rem;
}

.lecture-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #5260bf, #667eea);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.lecture-date {
    background: #e2e8f0;
    color: #4a5568;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.lecture-card-body {
    padding: 0 1.5rem 1rem;
}

.lecture-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.lecture-description {
    color: #718096;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.lecture-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.course-badge, .time-badge {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    color: #718096;
}

.course-badge {
    color: #5260bf;
    font-weight: 500;
}

.lecture-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e2e8f0;
    background: white;
}

.file-status {
    font-size: 0.8rem;
    font-weight: 500;
}

.file-available {
    color: #28a745;
}

.file-missing {
    color: #6c757d;
}

.lecture-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-btn.download {
    background: #e8f5e8;
    color: #28a745;
}

.action-btn.edit {
    background: #e3f2fd;
    color: #1976d2;
}

.action-btn.duplicate {
    background: #fff3e0;
    color: #f57c00;
}

.action-btn.delete {
    background: #ffebee;
    color: #d32f2f;
}

.action-btn.image {
    background: #fff3e0;
    color: #f57c00;
}

.action-btn:hover {
    transform: scale(1.1);
}

/* Lectures List View */
.lectures-list {
    padding: 2rem;
}

.lecture-list-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: #f7fafc;
    border-radius: 15px;
    border: 1px solid #e2e8f0;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.lecture-list-item:hover {
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateX(5px);
}

.lecture-list-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #5260bf, #667eea);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.lecture-list-content {
    flex-grow: 1;
}

.lecture-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.lecture-list-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.lecture-list-date {
    color: #718096;
    font-size: 0.9rem;
}

.lecture-list-description {
    color: #718096;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.lecture-list-meta {
    display: flex;
    gap: 1rem;
}

.course-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.file-tag {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.file-tag.available {
    background: #e8f5e8;
    color: #28a745;
}

.file-tag.missing {
    background: #f5f5f5;
    color: #6c757d;
}

.lecture-list-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.list-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.list-action-btn.download {
    background: #e8f5e8;
    color: #28a745;
}

.list-action-btn.edit {
    background: #e3f2fd;
    color: #1976d2;
}

.list-action-btn.duplicate {
    background: #fff3e0;
    color: #f57c00;
}

.list-action-btn.delete {
    background: #ffebee;
    color: #d32f2f;
}

.list-action-btn.image {
    background: #fff3e0;
    color: #f57c00;
}

.list-action-btn:hover {
    transform: scale(1.1);
}

/* Pagination */
.pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-top: 1px solid #e2e8f0;
    background: #f7fafc;
}

.pagination-info {
    color: #718096;
    font-size: 0.9rem;
}

.pagination-modern {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
}

.pagination-item {
    display: flex;
}

.pagination-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: white;
    color: #4a5568;
    text-decoration: none;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    font-weight: 500;
}

.pagination-link:hover {
    background: #5260bf;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(82, 96, 191, 0.3);
}

.pagination-item.active .pagination-link {
    background: #5260bf;
    color: white;
    border-color: #5260bf;
}

.pagination-link.prev,
.pagination-link.next {
    font-weight: 600;
}

/* Empty State */
.empty-state-section {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
    padding: 4rem 2rem;
    text-align: center;
}

.empty-state-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #f7fafc, #e2e8f0);
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: #718096;
    font-size: 3rem;
}

.empty-state-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.empty-state-description {
    color: #718096;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-state-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-modern {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-modern.btn-primary {
    background: linear-gradient(135deg, #5260bf, #667eea);
    color: white;
}

.btn-modern.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(82, 96, 191, 0.3);
    color: white;
}

.btn-modern.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-modern.btn-secondary:hover {
    background: #cbd5e0;
    color: #2d3748;
}

/* Notifications */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    border-left: 4px solid #28a745;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.notification.error {
    border-left-color: #dc3545;
    color: #721c24;
}

.notification.success {
    border-left-color: #28a745;
    color: #155724;
}

.notification.show {
    transform: translateX(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 4rem 0 3rem 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-actions .btn {
        display: block;
        margin-bottom: 1rem;
        width: 100%;
    }

    .hero-stats {
        margin-top: 2rem;
    }

    .main-content {
        margin-top: -1rem;
        border-radius: 1rem 1rem 0 0;
    }

    .search-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .lectures-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .lecture-list-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .lecture-list-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .lecture-list-actions {
        align-self: stretch;
        justify-content: center;
    }

    .pagination-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .pagination-modern {
        justify-content: center;
        flex-wrap: wrap;
    }

    .empty-state-section {
        padding: 3rem 1rem;
    }

    .empty-state-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-modern {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 3rem 0 2rem 0;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .search-header, .lectures-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .view-toggle {
        align-self: stretch;
        justify-content: center;
    }

    .lecture-card {
        margin-bottom: 1rem;
    }

    .lecture-actions, .lecture-list-actions {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .action-btn, .list-action-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }

    .notification {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        transform: translateY(-100%);
    }

    .notification.show {
        transform: translateY(0);
    }
}
