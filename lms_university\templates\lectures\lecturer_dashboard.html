{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم المدرس{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/lecturer_dashboard.css' %}">
<link rel="stylesheet" href="{% static 'css/custom_buttons.css' %}">
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-8">
                <div class="hero-content">
                    <div class="welcome-badge">
                        <i class="bi bi-mortarboard-fill me-2"></i>
                        مرحباً بك في لوحة التحكم
                    </div>
                    <h1 class="hero-title">
                        أهلاً وسهلاً، {{ user.full_name }}
                    </h1>
                    <p class="hero-subtitle">
                        إدارة محاضراتك ومقرراتك بكل سهولة ومرونة
                    </p>
                    <div class="hero-actions">
                        <a href="{% url 'lectures:add_lecture' %}" class="btn btn-primary btn-lg me-3">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة محاضرة جديدة
                        </a>
                        <a href="{% url 'lectures:lecturer_lectures' %}" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-list-ul me-2"></i>
                            عرض المحاضرات
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-illustration">
                    <div class="floating-card">
                        <i class="bi bi-journal-text"></i>
                        <span>{{ total_lectures }}</span>
                        <small>محاضرة</small>
                    </div>
                    <div class="floating-card delay-1">
                        <i class="bi bi-book"></i>
                        <span>{{ total_courses }}</span>
                        <small>مقرر</small>
                    </div>
                    <div class="floating-card delay-2">
                        <i class="bi bi-people"></i>
                        <span>{{ recent_lectures|length }}</span>
                        <small>حديثة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="main-content">
<div class="container py-5">

    <!-- Enhanced Statistics Cards -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card-modern">
                <div class="stats-card-header">
                    <div class="stats-icon-modern bg-gradient-primary">
                        <i class="bi bi-book"></i>
                    </div>
                    <div class="stats-trend">
                        <i class="bi bi-arrow-up text-success"></i>
                        <span class="text-success">+12%</span>
                    </div>
                </div>
                <div class="stats-content">
                    <h2 class="stats-number-modern">{{ total_courses }}</h2>
                    <p class="stats-label-modern">المقررات النشطة</p>
                    <div class="stats-progress">
                        <div class="progress-bar bg-primary" style="width: 85%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card-modern">
                <div class="stats-card-header">
                    <div class="stats-icon-modern bg-gradient-success">
                        <i class="bi bi-journal-text"></i>
                    </div>
                    <div class="stats-trend">
                        <i class="bi bi-arrow-up text-success"></i>
                        <span class="text-success">+8%</span>
                    </div>
                </div>
                <div class="stats-content">
                    <h2 class="stats-number-modern">{{ total_lectures }}</h2>
                    <p class="stats-label-modern">إجمالي المحاضرات</p>
                    <div class="stats-progress">
                        <div class="progress-bar bg-success" style="width: 92%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card-modern">
                <div class="stats-card-header">
                    <div class="stats-icon-modern bg-gradient-info">
                        <i class="bi bi-calendar-event"></i>
                    </div>
                    <div class="stats-trend">
                        <i class="bi bi-arrow-up text-success"></i>
                        <span class="text-success">+5%</span>
                    </div>
                </div>
                <div class="stats-content">
                    <h2 class="stats-number-modern">{{ recent_lectures|length }}</h2>
                    <p class="stats-label-modern">المحاضرات الأخيرة</p>
                    <div class="stats-progress">
                        <div class="progress-bar bg-info" style="width: 78%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card-modern">
                <div class="stats-card-header">
                    <div class="stats-icon-modern bg-gradient-warning">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stats-trend">
                        <i class="bi bi-arrow-up text-success"></i>
                        <span class="text-success">+15%</span>
                    </div>
                </div>
                <div class="stats-content">
                    <h2 class="stats-number-modern">{{ total_courses|add:total_lectures }}</h2>
                    <p class="stats-label-modern">إجمالي المحتوى</p>
                    <div class="stats-progress">
                        <div class="progress-bar bg-warning" style="width: 95%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Quick Actions -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="quick-actions-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="bi bi-lightning-charge-fill me-2"></i>
                        إجراءات سريعة
                    </h3>
                    <p class="section-subtitle">الوصول السريع للمهام الأساسية</p>
                </div>

                <div class="quick-actions-grid">
                    <div class="quick-action-card primary">
                        <div class="action-icon">
                            <i class="bi bi-plus-circle-fill"></i>
                        </div>
                        <div class="action-content">
                            <h4>إضافة محاضرة</h4>
                            <p>إنشاء محاضرة جديدة ورفع المحتوى</p>
                            <a href="{% url 'lectures:add_lecture' %}" class="action-btn">
                                ابدأ الآن
                                <i class="bi bi-arrow-left ms-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="quick-action-card success">
                        <div class="action-icon">
                            <i class="bi bi-collection-fill"></i>
                        </div>
                        <div class="action-content">
                            <h4>إدارة المحاضرات</h4>
                            <p>عرض وتعديل المحاضرات الموجودة</p>
                            <a href="{% url 'lectures:lecturer_lectures' %}" class="action-btn">
                                عرض الكل
                                <i class="bi bi-arrow-left ms-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="quick-action-card info">
                        <div class="action-icon">
                            <i class="bi bi-search"></i>
                        </div>
                        <div class="action-content">
                            <h4>البحث المتقدم</h4>
                            <p>البحث في المحاضرات والمقررات</p>
                            <a href="{% url 'lectures:lecturer_lectures' %}?search=" class="action-btn">
                                ابحث الآن
                                <i class="bi bi-arrow-left ms-2"></i>
                            </a>
                        </div>
                    </div>

                    <div class="quick-action-card warning">
                        <div class="action-icon">
                            <i class="bi bi-gear-fill"></i>
                        </div>
                        <div class="action-content">
                            <h4>الإعدادات</h4>
                            <p>تخصيص الملف الشخصي والإعدادات</p>
                            <a href="{% url 'core:dashboard' %}" class="action-btn">
                                الإعدادات
                                <i class="bi bi-arrow-left ms-2"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content Sections -->
    <div class="row">
        <!-- Recent Lectures -->
        <div class="col-lg-8 mb-4">
            <div class="content-card">
                <div class="content-header">
                    <div class="header-info">
                        <h3 class="content-title">
                            <i class="bi bi-clock-history me-2"></i>
                            أحدث المحاضرات
                        </h3>
                        <p class="content-subtitle">آخر المحاضرات التي تم إضافتها</p>
                    </div>
                    <a href="{% url 'lectures:lecturer_lectures' %}" class="view-all-btn">
                        عرض الكل
                        <i class="bi bi-arrow-left ms-2"></i>
                    </a>
                </div>

                <div class="content-body">
                    {% if recent_lectures %}
                        <div class="lectures-timeline">
                            {% for lecture in recent_lectures %}
                            <div class="timeline-item">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <div class="lecture-card-mini">
                                        {% if lecture.image %}
                                        <div class="lecture-mini-image">
                                            <img src="{{ lecture.image.url }}" alt="{{ lecture.title }}">
                                        </div>
                                        {% else %}
                                        <div class="lecture-mini-icon">
                                            <i class="bi bi-play-circle-fill"></i>
                                        </div>
                                        {% endif %}
                                        <div class="lecture-info">
                                            <h4 class="lecture-title">{{ lecture.title }}</h4>
                                            <div class="lecture-meta">
                                                <span class="course-badge">{{ lecture.course.name }}</span>
                                                <span class="time-badge">
                                                    <i class="bi bi-clock me-1"></i>
                                                    {{ lecture.date|timesince }} مضت
                                                </span>
                                            </div>
                                            {% if lecture.description %}
                                            <p class="lecture-desc">{{ lecture.description|truncatewords:15 }}</p>
                                            {% endif %}
                                        </div>
                                        <div class="lecture-actions">
                                            <a href="{% url 'lectures:edit_lecture' lecture.id %}"
                                               class="action-btn-mini edit" title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            {% if lecture.file %}
                                            <a href="{{ lecture.file.url }}" target="_blank"
                                               class="action-btn-mini download" title="تحميل">
                                                <i class="bi bi-download"></i>
                                            </a>
                                            {% endif %}
                                            <div class="action-btn-mini view" title="عرض">
                                                <i class="bi bi-eye"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="bi bi-journal-plus"></i>
                            </div>
                            <h4>لا توجد محاضرات بعد</h4>
                            <p>ابدأ بإضافة أول محاضرة لك</p>
                            <a href="{% url 'lectures:add_lecture' %}" class="btn btn-primary btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>
                                إضافة أول محاضرة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- My Courses -->
        <div class="col-lg-4 mb-4">
            <div class="content-card">
                <div class="content-header">
                    <div class="header-info">
                        <h3 class="content-title">
                            <i class="bi bi-book me-2"></i>
                            مقرراتي
                        </h3>
                        <p class="content-subtitle">المقررات المسندة إليك</p>
                    </div>
                </div>

                <div class="content-body">
                    {% if assigned_courses %}
                        <div class="courses-grid">
                            {% for course in assigned_courses %}
                            <div class="course-card-mini">
                                <div class="course-header">
                                    <div class="course-icon">
                                        <i class="bi bi-journal-bookmark-fill"></i>
                                    </div>
                                    <div class="course-status active">
                                        <i class="bi bi-check-circle-fill"></i>
                                    </div>
                                </div>
                                <div class="course-content">
                                    <h4 class="course-name">{{ course.name }}</h4>
                                    <p class="course-code">{{ course.code }}</p>
                                    <div class="course-stats">
                                        <div class="stat-item">
                                            <i class="bi bi-journal-text"></i>
                                            <span>{{ course.lectures.count }} محاضرة</span>
                                        </div>
                                        <div class="stat-item">
                                            <i class="bi bi-people"></i>
                                            <span>{{ course.department.name }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="course-actions">
                                    <a href="{% url 'lectures:add_lecture' %}" class="course-action-btn">
                                        <i class="bi bi-plus"></i>
                                    </a>
                                    <a href="{% url 'lectures:lecturer_lectures' %}?course={{ course.id }}" class="course-action-btn">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="bi bi-book"></i>
                            </div>
                            <h4>لا توجد مقررات مسندة</h4>
                            <p>تواصل مع مدير النظام لإسناد مقررات</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}







