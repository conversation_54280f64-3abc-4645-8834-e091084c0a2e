from django.middleware.csrf import CsrfViewMiddleware
from django.conf import settings
import logging
from django.contrib import messages
from django.shortcuts import redirect

logger = logging.getLogger(__name__)

class CustomCSRFMiddleware(CsrfViewMiddleware):
    """
    Middleware مخصص للتعامل مع مشاكل CSRF في views المصادقة
    """
    
    def process_view(self, request, callback, callback_args, callback_kwargs):
        """
        معالجة خاصة لـ views المصادقة في بيئة التطوير
        """
        
        # في بيئة التطوير، كن أكثر تساهلاً مع CSRF
        if settings.DEBUG:
            # قائمة بـ views التي نريد تجاهل CSRF لها مؤقتاً
            exempt_views = [
                'accounts.views.verify_otp',
                'accounts.views.resend_otp',
                'accounts.views.login_view',
            ]
            
            view_name = f"{callback.__module__}.{callback.__name__}"
            
            if view_name in exempt_views:
                logger.warning(f"CSRF check bypassed for {view_name} in development mode")
                return None
        
        # استخدم المعالجة العادية للـ CSRF
        return super().process_view(request, callback, callback_args, callback_kwargs)
    
    def _reject(self, request, reason):
        """
        معالجة مخصصة لرفض CSRF
        """
        if settings.DEBUG:
            logger.error(f"CSRF rejection: {reason}")
            logger.error(f"Request path: {request.path}")
            logger.error(f"Request method: {request.method}")
            logger.error(f"POST data: {request.POST}")
            logger.error(f"CSRF token in POST: {request.POST.get('csrfmiddlewaretoken')}")
            logger.error(f"CSRF token in META: {request.META.get('CSRF_COOKIE')}")
        
        return super()._reject(request, reason)

class OneSessionPerUserMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if hasattr(request, "user") and request.user.is_authenticated:
            session_key = request.session.session_key
            if hasattr(request.user, 'last_session_key'):
                if request.user.last_session_key and request.user.last_session_key != session_key:
                    from django.contrib.auth import logout
                    logout(request)
                    messages.error(request, "لقد قمت بالفعل بتسجيل الدخول من نافذة أو جهاز آخر.<br>لا يمكنك الدخول من أكثر من متصفح أو جهاز في نفس الوقت.", extra_tags='safe')
                    return redirect('accounts:login')
        return self.get_response(request)
