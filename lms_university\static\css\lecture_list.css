/* Lecture List Page Styles */

/* Global Styles */
body {
    padding-top: 0 !important;
    font-family: 'Cairo', Arial, Tahoma, sans-serif !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #5260bf 0%, #764ba2 100%);
    color: white;
    padding: 5rem 0 3rem 0;
    margin-top: 0;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

/* Main Content */
.main-content {
    position: relative;
    z-index: 2;
    background: #f8f9fa;
    margin-top: -2rem;
    border-radius: 2rem 2rem 0 0;
}

.min-vh-40 {
    min-height: 40vh;
}

/* Hero Content */
.hero-content {
    position: relative;
    z-index: 2;
}

.welcome-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    backdrop-filter: blur(10px);
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-actions .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.hero-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

/* Floating Cards Animation */
.hero-illustration {
    position: relative;
    height: 200px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.floating-card {
    position: relative;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    text-align: center;
    animation: float 6s ease-in-out infinite;
    transition: all 0.3s ease;
    width: 120px;
    margin: 0 10px;
}

.floating-card:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.25);
}

.floating-card:nth-child(1) {
    align-self: flex-start;
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    align-self: center;
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    align-self: flex-end;
    animation-delay: 4s;
}

.floating-card i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.floating-card span {
    font-size: 1.2rem;
    font-weight: bold;
    display: block;
}

.floating-card small {
    opacity: 0.8;
    font-size: 0.8rem;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

/* Section Header */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.section-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.lectures-count {
    background: white;
    padding: 1.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.count-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: #5260bf;
    line-height: 1;
}

.count-label {
    color: #718096;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Lectures Grid */
.lectures-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Modern Lecture Cards */
.lecture-card-modern {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.lecture-card-modern:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* Lecture Image */
.lecture-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.lecture-cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.lecture-card-modern:hover .lecture-cover-image {
    transform: scale(1.05);
}

.lecture-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 50%, rgba(0,0,0,0.4) 100%);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.5rem;
}

.lecture-card-header {
    padding: 1.5rem 1.5rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lecture-type-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #5260bf, #667eea);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.lecture-date-badge {
    background: rgba(255, 255, 255, 0.9);
    color: #2d3748;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

/* When no image is present */
.lecture-card-header .lecture-type-badge {
    background: linear-gradient(135deg, #5260bf, #667eea);
    backdrop-filter: none;
}

.lecture-card-header .lecture-date-badge {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    backdrop-filter: none;
}

.lecture-card-body {
    padding: 1.5rem;
}

.lecture-title {
    margin-bottom: 1rem;
}

.lecture-title a {
    color: #2d3748;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.lecture-title a:hover {
    color: #5260bf;
}

.lecture-meta {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    color: #718096;
    font-size: 0.9rem;
}

.course-name {
    color: #5260bf;
    font-weight: 500;
}

.course-code {
    color: #28a745;
    font-weight: 500;
}

.lecturer-name {
    color: #17a2b8;
    font-weight: 500;
}

.lecture-description {
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
    border-left: 4px solid #5260bf;
}

.lecture-description p {
    margin: 0;
    color: #4a5568;
    font-size: 0.9rem;
    line-height: 1.5;
}

.lecture-timestamp {
    display: flex;
    align-items: center;
    color: #a0aec0;
    font-size: 0.85rem;
    margin-bottom: 1rem;
}

.lecture-card-footer {
    padding: 0 1.5rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lecture-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.lecture-view-btn {
    flex: 1;
    background: linear-gradient(135deg, #5260bf, #667eea);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lecture-view-btn:hover {
    background: linear-gradient(135deg, #764ba2, #5260bf);
    transform: translateY(-2px);
    color: white;
}

.lecture-download-btn {
    width: 45px;
    height: 45px;
    background: #e8f5e8;
    color: #28a745;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.lecture-download-btn:hover {
    background: #28a745;
    color: white;
    transform: scale(1.1);
}

.lecture-image-btn {
    width: 45px;
    height: 45px;
    background: #fff3e0;
    color: #f57c00;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.lecture-image-btn:hover {
    background: #f57c00;
    color: white;
    transform: scale(1.1);
}

/* Empty State */
.empty-state-section {
    padding: 4rem 0;
    text-align: center;
}

.empty-state-content {
    max-width: 600px;
    margin: 0 auto;
}

.empty-state-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #5260bf, #667eea);
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: white;
}

.empty-state-icon i {
    font-size: 3rem;
}

.empty-state-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.empty-state-description {
    color: #718096;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.empty-state-actions {
    margin-bottom: 3rem;
}

.empty-state-actions .btn {
    padding: 1rem 2rem;
    font-weight: 600;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.empty-state-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

.empty-state-suggestions {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: right;
}

.empty-state-suggestions h4 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-weight: 600;
}

.empty-state-suggestions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.empty-state-suggestions li {
    padding: 0.5rem 0;
    color: #718096;
    position: relative;
    padding-right: 2rem;
}

.empty-state-suggestions li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: #28a745;
    font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0 2rem 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-actions .btn {
        width: 100%;
        margin-bottom: 1rem;
    }

    .hero-illustration {
        height: 150px;
        padding: 0 10px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .floating-card {
        width: 90px;
        padding: 1rem;
        margin: 5px;
    }

    .floating-card i {
        font-size: 1.2rem;
    }

    .floating-card span {
        font-size: 0.9rem;
    }

    .floating-card small {
        font-size: 0.7rem;
    }

    .main-content {
        margin-top: -1rem;
        border-radius: 1rem 1rem 0 0;
    }

    .lectures-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .section-header {
        text-align: center;
    }

    .lectures-count {
        margin-top: 2rem;
    }

    .lecture-card-modern {
        margin-bottom: 1rem;
    }

    .lecture-meta {
        gap: 0.5rem;
    }

    .lecture-card-footer {
        flex-direction: column;
        gap: 0.75rem;
    }

    .lecture-download-btn {
        width: 100%;
        height: 45px;
    }

    .min-vh-40 {
        min-height: auto;
    }

    .empty-state-icon {
        width: 100px;
        height: 100px;
    }

    .empty-state-icon i {
        font-size: 2.5rem;
    }

    .empty-state-title {
        font-size: 1.5rem;
    }

    .empty-state-suggestions {
        text-align: center;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 2rem 0 1.5rem 0;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .lecture-card-modern {
        border-radius: 15px;
    }

    .lecture-card-header {
        padding: 1rem 1rem 0;
    }

    .lecture-card-body {
        padding: 1rem;
    }

    .lecture-card-footer {
        padding: 0 1rem 1rem;
    }

    .lectures-count {
        padding: 1rem;
    }

    .count-number {
        font-size: 2rem;
    }
}
