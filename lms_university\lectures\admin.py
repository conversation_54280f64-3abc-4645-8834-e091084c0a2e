from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import Lecture

@admin.register(Lecture)
class LectureAdmin(admin.ModelAdmin):
    """إدارة المحاضرات في Django Admin"""

    list_display = (
        'title',
        'course_info',
        'lecturer_info',
        'date',
        'image_preview',
        'has_file_badge',
        'actions_column'
    )

    list_filter = (
        'course',
        'lecturer',
        'date',
        'course__department',
        'course__department__faculty'
    )

    search_fields = (
        'title',
        'description',
        'course__name',
        'course__code',
        'lecturer__full_name',
        'lecturer__email'
    )

    ordering = ('-date',)

    list_per_page = 20

    fieldsets = (
        ('معلومات المحاضرة', {
            'fields': ('title', 'description', 'course', 'lecturer')
        }),
        ('الملفات والصور', {
            'fields': ('file', 'image'),
        }),
    )

    readonly_fields = ('date',)

    def get_fieldsets(self, request, obj=None):
        """تخصيص fieldsets حسب ما إذا كان الكائن موجود أم لا"""
        fieldsets = super().get_fieldsets(request, obj)

        # إذا كان الكائن موجود (تعديل)، أضف قسم التاريخ
        if obj:
            fieldsets = fieldsets + (
                ('معلومات التاريخ', {
                    'fields': ('date',),
                    'classes': ('collapse',),
                    'description': 'تاريخ إنشاء المحاضرة (للقراءة فقط)'
                }),
            )

        return fieldsets

    def course_info(self, obj):
        """عرض معلومات المقرر"""
        return format_html(
            '<strong>{}</strong><br><small>{}</small>',
            obj.course.name,
            obj.course.code
        )
    course_info.short_description = 'المقرر'
    course_info.admin_order_field = 'course__name'

    def lecturer_info(self, obj):
        """عرض معلومات المحاضر"""
        return format_html(
            '<strong>{}</strong><br><small>{}</small>',
            obj.lecturer.full_name,
            obj.lecturer.email
        )
    lecturer_info.short_description = 'المحاضر'
    lecturer_info.admin_order_field = 'lecturer__full_name'

    def image_preview(self, obj):
        """عرض معاينة الصورة"""
        if obj.image:
            return format_html(
                '<img src="{}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;" />',
                obj.image.url
            )
        return "لا توجد صورة"
    image_preview.short_description = 'صورة المحاضرة'

    def has_file_badge(self, obj):
        """عرض حالة وجود الملف"""
        if obj.file:
            return format_html(
                '<span style="color: green;">✓ يوجد ملف</span>'
            )
        else:
            return format_html(
                '<span style="color: red;">✗ لا يوجد ملف</span>'
            )
    has_file_badge.short_description = 'الملف'

    def actions_column(self, obj):
        """عمود الإجراءات"""
        actions = []

        # رابط التعديل
        edit_url = reverse('admin:lectures_lecture_change', args=[obj.pk])
        actions.append(f'<a href="{edit_url}" class="btn btn-sm btn-primary">تعديل</a>')

        # رابط عرض الملف إذا وجد
        if obj.file:
            actions.append(f'<a href="{obj.file.url}" target="_blank" class="btn btn-sm btn-success">عرض الملف</a>')

        # رابط الحذف
        delete_url = reverse('admin:lectures_lecture_delete', args=[obj.pk])
        actions.append(f'<a href="{delete_url}" class="btn btn-sm btn-danger">حذف</a>')

        return format_html(' '.join(actions))
    actions_column.short_description = 'الإجراءات'

    def get_queryset(self, request):
        """تحسين الاستعلامات"""
        return super().get_queryset(request).select_related(
            'course',
            'lecturer',
            'course__department',
            'course__department__faculty'
        )

    def save_model(self, request, obj, form, change):
        """حفظ النموذج مع معالجة خاصة"""
        if not change:  # محاضرة جديدة
            # يمكن إضافة منطق خاص هنا
            pass
        super().save_model(request, obj, form, change)

    # الإجراءات المخصصة
    actions = ['duplicate_lectures']

    def duplicate_lectures(self, request, queryset):
        """تكرار المحاضرات المحددة"""
        count = 0
        for lecture in queryset:
            # إنشاء نسخة من المحاضرة
            lecture.pk = None
            lecture.title = f"نسخة من {lecture.title}"
            lecture.save()
            count += 1

        self.message_user(
            request,
            f'تم تكرار {count} محاضرة بنجاح.'
        )
    duplicate_lectures.short_description = "تكرار المحاضرات المحددة"
