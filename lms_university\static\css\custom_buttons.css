/* Custom <PERSON><PERSON> Styles for LMS University */

/* Add Lecture <PERSON><PERSON> - <PERSON> Gradient */
.hero-actions .btn-primary {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
    border: none !important;
    color: white !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.hero-actions .btn-primary:hover {
    background: linear-gradient(135deg, #fd7e14, #ffc107) !important;
    box-shadow: 0 10px 25px rgba(255, 193, 7, 0.4) !important;
    color: white !important;
    transform: translateY(-2px) !important;
}

.hero-actions .btn-primary:focus {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
    color: white !important;
}

.hero-actions .btn-primary:active {
    background: linear-gradient(135deg, #e0a800, #dc6502) !important;
    transform: translateY(0) !important;
    color: white !important;
}

/* Ensure text remains white in all states */
.hero-actions .btn-primary,
.hero-actions .btn-primary:hover,
.hero-actions .btn-primary:focus,
.hero-actions .btn-primary:active,
.hero-actions .btn-primary:visited {
    color: white !important;
    text-decoration: none !important;
}

/* Additional styling for better appearance */
.hero-actions .btn-primary i {
    color: white !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-actions .btn-primary {
        padding: 0.75rem 1.5rem !important;
        font-size: 1rem !important;
    }
}

@media (max-width: 576px) {
    .hero-actions .btn-primary {
        padding: 0.6rem 1.2rem !important;
        font-size: 0.9rem !important;
    }
}
