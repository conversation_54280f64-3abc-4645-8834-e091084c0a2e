from django.urls import path
from . import views

app_name = 'lectures'

urlpatterns = [
    # URLs للطلاب والعامة
    path('', views.lecture_list, name='lecture_list'),
    path('<int:pk>/', views.lecture_detail, name='lecture_detail'),

    # URLs للمدرسين
    path('lecturer/dashboard/', views.lecturer_dashboard, name='lecturer_dashboard'),
    path('lecturer/lectures/', views.lecturer_lectures, name='lecturer_lectures'),
    path('lecturer/add/', views.add_lecture, name='add_lecture'),
    path('lecturer/edit/<int:lecture_id>/', views.edit_lecture, name='edit_lecture'),
    path('lecturer/delete/<int:lecture_id>/', views.delete_lecture, name='delete_lecture'),
    path('lecturer/duplicate/<int:lecture_id>/', views.duplicate_lecture, name='duplicate_lecture'),
    path('lecturer/delete-image/<int:lecture_id>/', views.delete_lecture_image, name='delete_lecture_image'),
    path('lecturer/update-image/<int:lecture_id>/', views.update_lecture_image, name='update_lecture_image'),
]

