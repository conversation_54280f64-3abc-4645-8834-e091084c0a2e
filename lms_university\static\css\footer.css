.footer-custom a.text-white:hover {
    color: #fff700 !important;
    text-decoration: underline;
}
.footer-custom .bi {
    vertical-align: middle;
    font-size: 1.3rem;
}
/* Global Cairo Font Application for Footer */
.footer-custom * {
    font-family: 'Cairo', sans-serif !important;
}

.footer-custom {
    background: linear-gradient(90deg, #17439c 0%, #3755e7 100%);
    font-family: 'Cairo', sans-serif !important;
    width: 100%;
    box-shadow: 0 -2px 16px 0 rgba(252,87,94,0.08);
    padding-left: 0;
    padding-right: 0;
    border-radius: 0 !important;
    margin-top: auto;
}

.footer-custom a, .footer-custom p, .footer-custom span {
    font-family: 'Cairo', sans-serif !important;
}

.footer-custom .container {
    max-width: 100%;
    padding-left: 2rem;
    padding-right: 2rem;
}

.footer-link {
    transition: color 0.2s;
    text-decoration: none;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
}
.footer-link:hover, .footer-icon:hover {
    color: #ffd700 !important;
    text-decoration: underline;
}

.footer-icon {
    transition: color 0.2s, transform 0.2s;
    margin-left: 0.25rem;
    margin-right: 0.25rem;
}
.footer-icon:hover {
    color: #ff5252 !important;
    transform: scale(1.15);
}

@media (min-width: 768px) {
    .footer-custom .text-md-start {
        text-align: right !important;
    }
    .footer-custom .text-md-end {
        text-align: left !important;
    }
}
@media (max-width: 767.98px) {
    .footer-custom {
        border-radius: 0 !important;
        text-align: center;
        padding: 1.2rem 0.2rem;
    }
    .footer-custom .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    .footer-custom .row > div {
        text-align: center !important;
    }
    .footer-custom .d-flex {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.7rem;
        justify-content: center !important;
    }
}
