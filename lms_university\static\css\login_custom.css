/* Global Cairo Font Application */
* {
    font-family: 'Cairo', sans-serif !important;
}

body {
    min-height: 100vh;
    background: linear-gradient(120deg, #204795 0%, #3755e7 100%);
    background-repeat: no-repeat;
    background-attachment: fixed;
    font-family: 'Cairo', sans-serif !important;
}

body, .login-container, .login-title, .login-subtitle, .login-body, .form-control, .btn-login, h1, h2, h3, h4, h5, h6, p, span, div, a, input, button {
    font-family: 'Cairo', sans-serif !important;
}

/* Typography Weights */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
}

.btn, .btn-login {
    font-weight: 500;
}
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 1s;
}
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
.login-card {
    background: rgba(255, 255, 255, 0.93);
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(55, 85, 231, 0.18), 0 1.5px 8px rgba(0,0,0,0.07);
    padding: 2.5rem 2.2rem 2rem 2.2rem;
    max-width: 400px;
    width: 100%;
    animation: slideUp 0.8s cubic-bezier(.77,0,.18,1);
}
@keyframes slideUp {
    from { transform: translateY(40px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
.login-header {
    text-align: center;
    margin-bottom: 1.5rem;
}
.login-icon {
    background: linear-gradient(135deg, #17439c 0%, #3755e7 100%);
    border: 2px solid #e3f2fd;
    border-radius: 50%;
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    box-shadow: 0 2px 8px rgba(55, 85, 231, 0.13);
    font-size: 2.2rem;
    color: #fff;
}
.login-title {
    font-weight: 900;
    font-size: 2.1rem;
    color: #17439c;
    letter-spacing: 0.5px;
}
.login-subtitle {
    color: #3755e7;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}
.login-body {
    margin-top: 1.2rem;
}
.welcome-text h4 {
    font-weight: 700;
    color: #222;
    margin-bottom: 0.2rem;
}
.welcome-text p {
    color: #bfa900;
    margin-bottom: 1.2rem;
    font-size: 1rem;
}
.form-floating {
    margin-bottom: 1.1rem;
}
.form-control {
    border-radius: 10px;
    border: 1.5px solid #3755e7;
    background: #f7faff;
    font-size: 1.05rem;
    color: #222;
    transition: border-color 0.2s;
}
.form-control:focus {
    border-color: #17439c;
    box-shadow: 0 0 0 0.15rem rgba(55, 85, 231, 0.13);
    background: #f0f4ff;
}
.btn-login {
    width: 100%;
    background: linear-gradient(90deg, #3755e7 60%, #17439c 100%);
    color: #fff;
    font-weight: 900;
    border: none;
    border-radius: 10px;
    padding: 0.7rem 0;
    font-size: 1.15rem;
    margin-top: 0.5rem;
    margin-bottom: 0.7rem;
    box-shadow: 0 2px 8px rgba(55, 85, 231, 0.10);
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    position: relative;
    overflow: hidden;
}
.btn-login:hover, .btn-login:focus {
    background: linear-gradient(90deg, #17439c 0%, #3755e7 100%);
    color: #fff;
}
.loading-spinner {
    display: none;
    position: absolute;
    left: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.2rem;
    height: 1.2rem;
    border: 3px solid #ffe066;
    border-top: 3px solid #bfa900;
    border-bottom: 3px solid transparent;
    border-radius: 50%;
    animation: bounce-spin 0.7s cubic-bezier(.68,-0.55,.27,1.55) infinite;
    background: transparent;
}
@keyframes bounce-spin {
    0% {
        transform: translateY(-50%) scale(1) rotate(0deg);
    }
    30% {
        transform: translateY(-60%) scale(1.15) rotate(120deg);
    }
    60% {
        transform: translateY(-40%) scale(0.92) rotate(240deg);
    }
    100% {
        transform: translateY(-50%) scale(1) rotate(360deg);
    }
}
.btn-login.loading .loading-spinner {
    display: inline-block;
}
.field-error, .error-message {
    color: #d9534f;
    font-size: 0.97rem;
    margin-top: 0.2rem;
    margin-bottom: 0.2rem;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}
.security-note {
    color: #1976d2;
    font-size: 0.95rem;
    margin-top: 1.1rem;
    text-align: center;
    background: #fffbe6;
    border-radius: 8px;
    padding: 0.5rem 0.7rem;
    box-shadow: 0 1px 4px rgba(245, 221, 93, 0.08);
}
