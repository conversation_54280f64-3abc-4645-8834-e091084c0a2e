{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="hero-content">
                    <div class="welcome-badge">
                        <i class="bi bi-pencil-square me-2"></i>
                        تعديل المحاضرة
                    </div>
                    <h1 class="hero-title">
                        تحديث المحاضرة
                    </h1>
                    <p class="hero-subtitle">
                        تعديل وتحديث معلومات المحاضرة: "{{ lecture.title }}"
                    </p>
                    <div class="hero-actions">
                        <a href="{% url 'lectures:lecturer_lectures' %}" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-arrow-left me-2"></i>
                            العودة للمحاضرات
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-info">
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="bi bi-journal-bookmark"></i>
                        </div>
                        <div class="info-content">
                            <h4>{{ lecture.course.name }}</h4>
                            <p>{{ lecture.date|date:"d/m/Y H:i" }}</p>
                            {% if lecture.file %}
                            <div class="file-status available">
                                <i class="bi bi-file-earmark-check me-1"></i>
                                ملف متاح
                            </div>
                            {% else %}
                            <div class="file-status missing">
                                <i class="bi bi-file-earmark-x me-1"></i>
                                لا يوجد ملف
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="main-content">
<div class="container py-5">

    <!-- Enhanced Form Section -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Form Progress -->
            <div class="form-progress-section mb-4">
                <div class="progress-steps">
                    <div class="step active">
                        <div class="step-icon">
                            <i class="bi bi-info-circle"></i>
                        </div>
                        <span>معلومات أساسية</span>
                    </div>
                    <div class="step active">
                        <div class="step-icon">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                        <span>المحتوى والملفات</span>
                    </div>
                    <div class="step active">
                        <div class="step-icon">
                            <i class="bi bi-pencil-square"></i>
                        </div>
                        <span>التعديل والحفظ</span>
                    </div>
                </div>
            </div>

            <!-- Main Form Card -->
            <div class="form-card">
                <div class="form-header">
                    <div class="header-info">
                        <h3 class="form-title">
                            <i class="bi bi-pencil-square me-2"></i>
                            تحديث معلومات المحاضرة
                        </h3>
                        <p class="form-subtitle">قم بتعديل المعلومات والملفات حسب الحاجة</p>
                    </div>
                </div>

                <div class="form-body">
                    <form method="post" enctype="multipart/form-data" id="lectureForm" class="modern-form">
                        {% csrf_token %}

                        <!-- Course Selection -->
                        <div class="form-group-modern">
                            <div class="input-group-modern">
                                <div class="input-icon">
                                    <i class="bi bi-book"></i>
                                </div>
                                <div class="input-content">
                                    <label for="{{ form.course.id_for_label }}" class="modern-label">
                                        {{ form.course.label }}
                                        <span class="required-indicator">*</span>
                                    </label>
                                    {{ form.course }}
                                    {% if form.course.help_text %}
                                    <div class="input-help">{{ form.course.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            {% if form.course.errors %}
                            <div class="input-error">
                                {% for error in form.course.errors %}
                                <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Title -->
                        <div class="form-group-modern">
                            <div class="input-group-modern">
                                <div class="input-icon">
                                    <i class="bi bi-type"></i>
                                </div>
                                <div class="input-content">
                                    <label for="{{ form.title.id_for_label }}" class="modern-label">
                                        {{ form.title.label }}
                                        <span class="required-indicator">*</span>
                                    </label>
                                    {{ form.title }}
                                    {% if form.title.help_text %}
                                    <div class="input-help">{{ form.title.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            {% if form.title.errors %}
                            <div class="input-error">
                                {% for error in form.title.errors %}
                                <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Description -->
                        <div class="form-group-modern">
                            <div class="input-group-modern">
                                <div class="input-icon">
                                    <i class="bi bi-text-paragraph"></i>
                                </div>
                                <div class="input-content">
                                    <label for="{{ form.description.id_for_label }}" class="modern-label">
                                        {{ form.description.label }}
                                    </label>
                                    {{ form.description }}
                                    {% if form.description.help_text %}
                                    <div class="input-help">{{ form.description.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            {% if form.description.errors %}
                            <div class="input-error">
                                {% for error in form.description.errors %}
                                <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Current File Info -->
                        {% if lecture.file %}
                        <div class="form-group-modern">
                            <div class="current-file-section">
                                <div class="current-file-header">
                                    <div class="file-icon">
                                        <i class="bi bi-file-earmark-check"></i>
                                    </div>
                                    <div class="file-info">
                                        <h4>الملف الحالي</h4>
                                        <p>{{ lecture.file.name|slice:"9:" }}</p>
                                    </div>
                                </div>
                                <div class="file-actions">
                                    <a href="{{ lecture.file.url }}" target="_blank" class="file-action-btn download">
                                        <i class="bi bi-download me-2"></i>
                                        تحميل الملف
                                    </a>
                                    <button type="button" class="file-action-btn preview" onclick="previewFile('{{ lecture.file.url }}')">
                                        <i class="bi bi-eye me-2"></i>
                                        معاينة
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- File Upload -->
                        <div class="form-group-modern">
                            <div class="file-upload-modern">
                                <div class="upload-header">
                                    <div class="upload-icon">
                                        <i class="bi bi-cloud-upload"></i>
                                    </div>
                                    <div class="upload-content">
                                        <label for="{{ form.file.id_for_label }}" class="modern-label">
                                            {% if lecture.file %}تحديث الملف{% else %}{{ form.file.label }}{% endif %}
                                        </label>
                                        <p class="upload-subtitle">
                                            {% if lecture.file %}
                                            اختر ملفاً جديداً لاستبدال الملف الحالي
                                            {% else %}
                                            اسحب الملف هنا أو انقر للاختيار
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                                <div class="upload-area" id="uploadArea">
                                    {{ form.file }}
                                    <div class="upload-placeholder">
                                        {% if lecture.file %}
                                        <i class="bi bi-arrow-repeat"></i>
                                        <span>استبدال الملف الحالي</span>
                                        <small>أو اتركه فارغاً للاحتفاظ بالملف الحالي</small>
                                        {% else %}
                                        <i class="bi bi-file-earmark-plus"></i>
                                        <span>اختر ملف المحاضرة</span>
                                        <small>PDF, Word, PowerPoint, أو ملفات مضغوطة</small>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="upload-info" id="uploadInfo">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        {% if lecture.file %}
                                        اختر ملفاً جديداً لاستبدال الملف الحالي، أو اتركه فارغاً للاحتفاظ بالملف الحالي
                                        {% else %}
                                        {{ form.file.help_text }}
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                            {% if form.file.errors %}
                            <div class="input-error">
                                {% for error in form.file.errors %}
                                <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Image Upload -->
                        <div class="form-group-modern">
                            <div class="file-upload-modern">
                                <div class="upload-header">
                                    <div class="upload-icon">
                                        <i class="bi bi-image"></i>
                                    </div>
                                    <div class="upload-content">
                                        <label for="{{ form.image.id_for_label }}" class="modern-label">
                                            {% if lecture.image %}تحديث الصورة{% else %}{{ form.image.label }}{% endif %}
                                        </label>
                                        <p class="upload-subtitle">
                                            {% if lecture.image %}
                                            اختر صورة جديدة لاستبدال الصورة الحالية
                                            {% else %}
                                            اسحب الصورة هنا أو انقر للاختيار (اختياري)
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>

                                {% if lecture.image %}
                                <div class="current-image-preview">
                                    <div class="current-image-header">
                                        <i class="bi bi-image-fill"></i>
                                        <span>الصورة الحالية</span>
                                    </div>
                                    <div class="current-image-content">
                                        <img src="{{ lecture.image.url }}" alt="{{ lecture.title }}" class="current-image">
                                        <div class="current-image-actions">
                                            <a href="{{ lecture.image.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> عرض
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <div class="upload-area" id="imageUploadArea">
                                    {{ form.image }}
                                    <div class="upload-placeholder">
                                        {% if lecture.image %}
                                        <i class="bi bi-arrow-repeat"></i>
                                        <span>استبدال الصورة الحالية</span>
                                        <small>أو اتركها فارغة للاحتفاظ بالصورة الحالية</small>
                                        {% else %}
                                        <i class="bi bi-image-fill"></i>
                                        <span>اختر صورة غلاف للمحاضرة</span>
                                        <small>JPEG, PNG, GIF, WebP</small>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="upload-info" id="imageUploadInfo">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        {% if lecture.image %}
                                        اختر صورة جديدة لاستبدال الصورة الحالية، أو اتركها فارغة للاحتفاظ بالصورة الحالية
                                        {% else %}
                                        {{ form.image.help_text }}
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                            {% if form.image.errors %}
                            <div class="input-error">
                                {% for error in form.image.errors %}
                                <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- General Form Errors -->
                        {% if form.non_field_errors %}
                        <div class="form-errors">
                            {% for error in form.non_field_errors %}
                            <div class="error-item">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                {{ error }}
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Submit Buttons -->
                        <div class="form-actions">
                            <a href="{% url 'lectures:lecturer_lectures' %}" class="btn-modern btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>
                                إلغاء والعودة
                            </a>
                            <div class="action-group">
                                <button type="button" class="btn-modern btn-danger" onclick="confirmDelete()">
                                    <i class="bi bi-trash me-2"></i>
                                    حذف المحاضرة
                                </button>
                                <button type="submit" class="btn-modern btn-primary" id="submitBtn">
                                    <i class="bi bi-check-circle me-2"></i>
                                    حفظ التعديلات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-2">
            <div class="side-panel">
                <!-- Edit Tips -->
                <div class="tips-card">
                    <div class="tips-header">
                        <i class="bi bi-lightbulb"></i>
                        <h4>نصائح التعديل</h4>
                    </div>
                    <div class="tips-content">
                        <div class="tip-item">
                            <i class="bi bi-check-circle"></i>
                            <span>راجع المعلومات بعناية</span>
                        </div>
                        <div class="tip-item">
                            <i class="bi bi-check-circle"></i>
                            <span>تأكد من صحة العنوان</span>
                        </div>
                        <div class="tip-item">
                            <i class="bi bi-check-circle"></i>
                            <span>حدث الوصف إذا لزم الأمر</span>
                        </div>
                        <div class="tip-item">
                            <i class="bi bi-check-circle"></i>
                            <span>استبدل الملف عند الحاجة</span>
                        </div>
                    </div>
                </div>

                <!-- Lecture Info -->
                <div class="info-card-side">
                    <div class="info-header">
                        <i class="bi bi-info-circle"></i>
                        <h4>معلومات المحاضرة</h4>
                    </div>
                    <div class="info-content">
                        <div class="info-item">
                            <span class="info-label">تاريخ الإنشاء:</span>
                            <span class="info-value">{{ lecture.date|date:"d/m/Y" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">وقت الإنشاء:</span>
                            <span class="info-value">{{ lecture.date|date:"H:i" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">المقرر:</span>
                            <span class="info-value">{{ lecture.course.code }}</span>
                        </div>
                        {% if lecture.file %}
                        <div class="info-item">
                            <span class="info-label">حالة الملف:</span>
                            <span class="info-value text-success">متاح</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

{% endblock %}

{% block extra_css %}
<style>
    /* Global Styles */
    body {
        padding-top: 0 !important;
        font-family: 'Cairo', Arial, Tahoma, sans-serif !important;
    }

    /* Hero Section */
    .hero-section {
        background: linear-gradient(135deg, #5260bf 0%, #764ba2 100%);
        color: white;
        padding: 6rem 0 4rem 0;
        margin-top: 0;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    }

    /* Main Content */
    .main-content {
        position: relative;
        z-index: 2;
        background: #f8f9fa;
        margin-top: -2rem;
        border-radius: 2rem 2rem 0 0;
    }

    /* Hero Content */
    .hero-content {
        position: relative;
        z-index: 2;
    }

    .welcome-badge {
        display: inline-flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        backdrop-filter: blur(10px);
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .hero-actions .btn {
        padding: 0.75rem 2rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .hero-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }

    /* Hero Info */
    .hero-info {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    .info-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        width: 100%;
    }

    .info-card:hover {
        transform: scale(1.05);
        background: rgba(255, 255, 255, 0.25);
    }

    .info-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin: 0 auto 1rem;
    }

    .info-content h4 {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .info-content p {
        opacity: 0.9;
        margin-bottom: 1rem;
    }

    .file-status {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .file-status.available {
        background: rgba(40, 167, 69, 0.2);
        color: #28a745;
    }

    .file-status.missing {
        background: rgba(108, 117, 125, 0.2);
        color: #6c757d;
    }

    /* Form Progress */
    .form-progress-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background: #e2e8f0;
        z-index: 1;
    }

    .progress-steps::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;
        height: 2px;
        background: #5260bf;
        z-index: 2;
        transition: width 0.3s ease;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        position: relative;
        z-index: 3;
        background: white;
        padding: 0 1rem;
    }

    .step-icon {
        width: 50px;
        height: 50px;
        background: #5260bf;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        transition: all 0.3s ease;
    }

    .step span {
        font-size: 0.9rem;
        font-weight: 500;
        color: #5260bf;
        text-align: center;
    }

    /* Form Card */
    .form-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .form-header {
        padding: 2rem 2rem 1rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .form-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .form-subtitle {
        color: #718096;
        font-size: 0.9rem;
        margin: 0;
    }

    .form-body {
        padding: 2rem;
    }

    /* Modern Form Groups */
    .form-group-modern {
        margin-bottom: 2rem;
    }

    .input-group-modern {
        background: #f7fafc;
        border-radius: 15px;
        padding: 1.5rem;
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
        display: flex;
        gap: 1rem;
        align-items: flex-start;
    }

    .input-group-modern:focus-within {
        border-color: #5260bf;
        background: white;
        box-shadow: 0 0 0 3px rgba(82, 96, 191, 0.1);
    }

    .input-icon {
        width: 40px;
        height: 40px;
        background: #5260bf;
        color: white;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
        flex-shrink: 0;
        margin-top: 0.5rem;
    }

    .input-content {
        flex-grow: 1;
    }

    .modern-label {
        display: block;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .required-indicator {
        color: #e53e3e;
        margin-left: 0.25rem;
    }

    .input-help {
        font-size: 0.8rem;
        color: #718096;
        margin-top: 0.5rem;
    }

    .input-error {
        background: #fed7d7;
        color: #c53030;
        padding: 0.75rem;
        border-radius: 10px;
        margin-top: 0.5rem;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .input-error span {
        display: block;
        margin-bottom: 0.25rem;
    }

    .input-error span:last-child {
        margin-bottom: 0;
    }

    /* Form Controls */
    .form-control, .form-select {
        border: none;
        background: transparent;
        padding: 0.75rem 0;
        font-size: 1rem;
        color: #2d3748;
        border-bottom: 2px solid #e2e8f0;
        border-radius: 0;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        background: transparent;
        border-color: #5260bf;
        box-shadow: none;
        outline: none;
    }

    textarea.form-control {
        min-height: 100px;
        resize: vertical;
    }

    /* Current File Section */
    .current-file-section {
        background: #e8f5e8;
        border-radius: 15px;
        padding: 1.5rem;
        border: 2px solid #c6f6d5;
    }

    .current-file-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .file-icon {
        width: 60px;
        height: 60px;
        background: #28a745;
        color: white;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .file-info h4 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .file-info p {
        color: #718096;
        margin: 0;
        font-size: 0.9rem;
    }

    .file-actions {
        display: flex;
        gap: 0.75rem;
    }

    .file-action-btn {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 10px;
        text-decoration: none;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .file-action-btn.download {
        background: #28a745;
        color: white;
    }

    .file-action-btn.preview {
        background: #17a2b8;
        color: white;
    }

    .file-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    /* Current Image Preview */
    .current-image-preview {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #e2e8f0;
    }

    .current-image-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        color: #4a5568;
        font-weight: 500;
    }

    .current-image-header i {
        color: #5260bf;
    }

    .current-image-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .current-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        border: 2px solid #e2e8f0;
    }

    .current-image-actions {
        display: flex;
        gap: 0.5rem;
    }

    .current-image-actions .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .current-image-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    /* File Upload Modern */
    .file-upload-modern {
        background: #f7fafc;
        border-radius: 15px;
        padding: 1.5rem;
        border: 2px dashed #cbd5e0;
        transition: all 0.3s ease;
        position: relative;
    }

    .file-upload-modern:hover {
        border-color: #5260bf;
        background: #edf2f7;
    }

    .upload-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .upload-icon {
        width: 50px;
        height: 50px;
        background: #5260bf;
        color: white;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
    }

    .upload-content .modern-label {
        margin-bottom: 0.25rem;
    }

    .upload-subtitle {
        color: #718096;
        font-size: 0.9rem;
        margin: 0;
    }

    .upload-area {
        position: relative;
        min-height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px dashed #cbd5e0;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .upload-area:hover {
        border-color: #5260bf;
        background: rgba(82, 96, 191, 0.05);
    }

    .upload-area input[type="file"] {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .upload-placeholder {
        text-align: center;
        color: #718096;
    }

    .upload-placeholder i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: block;
        color: #5260bf;
    }

    .upload-placeholder span {
        display: block;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .upload-placeholder small {
        font-size: 0.8rem;
        opacity: 0.8;
    }

    .upload-info {
        margin-top: 1rem;
        padding: 0.75rem;
        background: #e2e8f0;
        border-radius: 8px;
        font-size: 0.8rem;
        color: #4a5568;
    }

    /* Form Errors */
    .form-errors {
        background: #fed7d7;
        border: 1px solid #feb2b2;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .error-item {
        color: #c53030;
        font-weight: 500;
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .error-item:last-child {
        margin-bottom: 0;
    }

    /* Form Actions */
    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 2rem;
        border-top: 1px solid #e2e8f0;
        margin-top: 2rem;
    }

    .action-group {
        display: flex;
        gap: 1rem;
    }

    .btn-modern {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 1rem;
    }

    .btn-modern.btn-primary {
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
    }

    .btn-modern.btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(82, 96, 191, 0.3);
        color: white;
    }

    .btn-modern.btn-secondary {
        background: #e2e8f0;
        color: #4a5568;
    }

    .btn-modern.btn-secondary:hover {
        background: #cbd5e0;
        color: #2d3748;
    }

    .btn-modern.btn-danger {
        background: #e53e3e;
        color: white;
    }

    .btn-modern.btn-danger:hover {
        background: #c53030;
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(229, 62, 62, 0.3);
    }

    /* Side Panel */
    .side-panel {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .tips-card, .info-card-side {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .tips-header, .info-header {
        padding: 1.5rem 1.5rem 1rem;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .tips-header i, .info-header i {
        width: 35px;
        height: 35px;
        background: linear-gradient(135deg, #5260bf, #667eea);
        color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .tips-header h4, .info-header h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
    }

    .tips-content, .info-content {
        padding: 1.5rem;
    }

    .tip-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
        padding: 0.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .tip-item:hover {
        background: #f7fafc;
    }

    .tip-item:last-child {
        margin-bottom: 0;
    }

    .tip-item i {
        color: #28a745;
        font-size: 0.9rem;
    }

    .tip-item span {
        font-size: 0.9rem;
        color: #4a5568;
        font-weight: 500;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f1f5f9;
    }

    .info-item:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }

    .info-label {
        font-size: 0.8rem;
        color: #718096;
        font-weight: 500;
    }

    .info-value {
        font-size: 0.9rem;
        color: #2d3748;
        font-weight: 600;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-section {
            padding: 4rem 0 3rem 0;
        }

        .hero-title {
            font-size: 2rem;
        }

        .hero-subtitle {
            font-size: 1rem;
        }

        .hero-actions .btn {
            display: block;
            width: 100%;
        }

        .info-card {
            margin-top: 2rem;
        }

        .main-content {
            margin-top: -1rem;
            border-radius: 1rem 1rem 0 0;
        }

        .form-progress-section {
            margin-bottom: 2rem;
        }

        .progress-steps {
            flex-direction: column;
            gap: 1rem;
        }

        .progress-steps::before,
        .progress-steps::after {
            display: none;
        }

        .step {
            flex-direction: row;
            padding: 0;
            background: transparent;
        }

        .input-group-modern {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .input-icon {
            align-self: flex-start;
            margin-top: 0;
        }

        .current-file-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .file-actions {
            align-self: stretch;
            justify-content: center;
        }

        .form-actions {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .action-group {
            flex-direction: column;
            gap: 0.75rem;
        }

        .btn-modern {
            width: 100%;
            justify-content: center;
        }

        .side-panel {
            margin-top: 2rem;
        }
    }

    @media (max-width: 576px) {
        .hero-section {
            padding: 3rem 0 2rem 0;
        }

        .hero-title {
            font-size: 1.75rem;
        }

        .form-body {
            padding: 1.5rem;
        }

        .input-group-modern {
            padding: 1rem;
        }

        .current-file-section,
        .file-upload-modern {
            padding: 1rem;
        }

        .upload-area {
            min-height: 100px;
        }

        .file-action-btn {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
        }

        .tips-content,
        .info-content {
            padding: 1rem;
        }

        .tip-item {
            padding: 0.25rem;
        }
    }

    /* Notifications */
    .notification {
        position: fixed;
        top: 2rem;
        right: 2rem;
        background: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        border-left: 4px solid #28a745;
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        display: flex;
        align-items: center;
        font-weight: 500;
    }

    .notification.error {
        border-left-color: #dc3545;
        color: #721c24;
    }

    .notification.success {
        border-left-color: #28a745;
        color: #155724;
    }

    .notification.show {
        transform: translateX(0);
    }

    @media (max-width: 576px) {
        .notification {
            top: 1rem;
            right: 1rem;
            left: 1rem;
            transform: translateY(-100%);
        }

        .notification.show {
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('lectureForm');
    const submitBtn = document.getElementById('submitBtn');
    const fileInput = document.querySelector('input[type="file"]');
    const uploadArea = document.getElementById('uploadArea');
    const uploadInfo = document.getElementById('uploadInfo');

    // Form submission handling
    if (form) {
        form.addEventListener('submit', function(e) {
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
            }
        });
    }

    // Image upload elements
    const imageInput = document.querySelector('input[type="file"][name="image"]');
    const imageUploadArea = document.getElementById('imageUploadArea');
    const imageUploadInfo = document.getElementById('imageUploadInfo');

    // Enhanced file upload with drag & drop
    if (fileInput && uploadArea) {
        // Drag & Drop functionality
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#5260bf';
            uploadArea.style.background = 'rgba(82, 96, 191, 0.1)';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#cbd5e0';
            uploadArea.style.background = '';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#cbd5e0';
            uploadArea.style.background = '';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                updateFileInfo(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                updateFileInfo(file);
            }
        });
    }

    // Update file info display
    function updateFileInfo(file) {
        if (uploadInfo) {
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            const fileType = file.type || 'غير محدد';

            uploadInfo.innerHTML = `
                <div class="file-selected">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    <strong>تم اختيار ملف جديد:</strong>
                    <div class="file-details mt-1">
                        <span class="file-name">${file.name}</span>
                        <span class="file-size">(${fileSize} MB)</span>
                    </div>
                </div>
            `;

            // Add some styling
            const fileSelected = uploadInfo.querySelector('.file-selected');
            if (fileSelected) {
                fileSelected.style.background = '#d4edda';
                fileSelected.style.color = '#155724';
                fileSelected.style.padding = '0.75rem';
                fileSelected.style.borderRadius = '8px';
                fileSelected.style.border = '1px solid #c3e6cb';
            }
        }
    }

    // Enhanced image upload with drag & drop
    if (imageInput && imageUploadArea) {
        // Drag & Drop functionality for images
        imageUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            imageUploadArea.style.borderColor = '#5260bf';
            imageUploadArea.style.background = '#edf2f7';
        });

        imageUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            imageUploadArea.style.borderColor = '#cbd5e0';
            imageUploadArea.style.background = '#f7fafc';
        });

        imageUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            imageUploadArea.style.borderColor = '#cbd5e0';
            imageUploadArea.style.background = '#f7fafc';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                imageInput.files = files;
                updateImageInfo(files[0]);
            }
        });

        // Image input change
        imageInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                updateImageInfo(e.target.files[0]);
            }
        });

        function updateImageInfo(file) {
            const placeholder = imageUploadArea.querySelector('.upload-placeholder');
            if (placeholder) {
                // Check if it's an image file
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        placeholder.innerHTML = `
                            <div class="image-preview">
                                <img src="${e.target.result}" alt="معاينة الصورة" style="max-width: 100px; max-height: 100px; border-radius: 8px;">
                            </div>
                            <span class="text-success">تم اختيار الصورة الجديدة</span>
                            <small>${file.name}</small>
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                        `;
                    };
                    reader.readAsDataURL(file);
                } else {
                    placeholder.innerHTML = `
                        <i class="bi bi-image-fill text-success"></i>
                        <span class="text-success">تم اختيار الملف</span>
                        <small>${file.name}</small>
                        <small class="text-muted">${formatFileSize(file.size)}</small>
                    `;
                }
            }
        }
    }

    // Form validation
    function validateForm() {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                showFieldError(field, 'هذا الحقل مطلوب');
            } else {
                clearFieldError(field);
            }
        });

        return isValid;
    }

    function showFieldError(field, message) {
        const inputGroup = field.closest('.input-group-modern');
        if (inputGroup) {
            inputGroup.style.borderColor = '#e53e3e';

            let errorDiv = inputGroup.parentNode.querySelector('.input-error');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.className = 'input-error';
                inputGroup.parentNode.appendChild(errorDiv);
            }
            errorDiv.innerHTML = `<span>${message}</span>`;
        }
    }

    function clearFieldError(field) {
        const inputGroup = field.closest('.input-group-modern');
        if (inputGroup) {
            inputGroup.style.borderColor = '#e2e8f0';
            const errorDiv = inputGroup.parentNode.querySelector('.input-error');
            if (errorDiv) {
                errorDiv.remove();
            }
        }
    }

    // Smooth scrolling for form sections
    const formGroups = document.querySelectorAll('.form-group-modern');
    formGroups.forEach((group, index) => {
        const input = group.querySelector('input, select, textarea');
        if (input) {
            input.addEventListener('focus', function() {
                setTimeout(() => {
                    group.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);
            });
        }
    });

    // Auto-save draft (optional feature)
    let autoSaveTimeout;
    const formInputs = form.querySelectorAll('input, select, textarea');

    formInputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                saveDraft();
            }, 2000); // Save after 2 seconds of inactivity
        });
    });

    function saveDraft() {
        const formData = new FormData(form);
        const draftData = {};

        for (let [key, value] of formData.entries()) {
            if (key !== 'csrfmiddlewaretoken' && key !== 'file') {
                draftData[key] = value;
            }
        }

        localStorage.setItem('lecture_edit_draft_{{ lecture.id }}', JSON.stringify(draftData));
        showNotification('تم حفظ المسودة تلقائياً', 'info');
    }

    // Load draft on page load
    function loadDraft() {
        const draftData = localStorage.getItem('lecture_edit_draft_{{ lecture.id }}');
        if (draftData) {
            try {
                const data = JSON.parse(draftData);
                Object.keys(data).forEach(key => {
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field && field.value !== data[key]) {
                        field.value = data[key];
                        // Show notification that draft was loaded
                        setTimeout(() => {
                            showNotification('تم استعادة المسودة المحفوظة', 'info');
                        }, 1000);
                    }
                });
            } catch (e) {
                console.error('Error loading draft:', e);
            }
        }
    }

    // Load draft after a short delay
    setTimeout(loadDraft, 500);

    // Clear draft on successful submission
    form.addEventListener('submit', function() {
        localStorage.removeItem('lecture_edit_draft_{{ lecture.id }}');
    });
});

// Enhanced delete confirmation
function confirmDelete() {
    const modal = document.createElement('div');
    modal.className = 'delete-modal';
    modal.innerHTML = `
        <div class="delete-modal-content">
            <div class="delete-modal-header">
                <i class="bi bi-exclamation-triangle text-danger"></i>
                <h3>تأكيد الحذف</h3>
            </div>
            <div class="delete-modal-body">
                <p>هل أنت متأكد من حذف هذه المحاضرة؟</p>
                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                <div class="lecture-info">
                    <strong>المحاضرة:</strong> {{ lecture.title }}
                </div>
            </div>
            <div class="delete-modal-actions">
                <button type="button" class="btn-modern btn-secondary" onclick="closeDeleteModal()">
                    إلغاء
                </button>
                <button type="button" class="btn-modern btn-danger" onclick="proceedDelete()">
                    <i class="bi bi-trash me-2"></i>
                    حذف المحاضرة
                </button>
            </div>
        </div>
    `;

    // Add modal styles
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    `;

    const content = modal.querySelector('.delete-modal-content');
    content.style.cssText = `
        background: white;
        border-radius: 15px;
        padding: 2rem;
        max-width: 400px;
        width: 90%;
        text-align: center;
    `;

    document.body.appendChild(modal);
}

function closeDeleteModal() {
    const modal = document.querySelector('.delete-modal');
    if (modal) {
        modal.remove();
    }
}

function proceedDelete() {
    window.location.href = '{% url "lectures:delete_lecture" lecture.id %}';
}

// File preview function
function previewFile(url) {
    window.open(url, '_blank');
}

// Notification system
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}
</script>
{% endblock %}
