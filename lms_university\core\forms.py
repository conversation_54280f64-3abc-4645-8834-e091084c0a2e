from django import forms
from django.core.exceptions import ValidationError
from .models import Course, Faculty, Department

class CourseForm(forms.ModelForm):
    """نموذج إضافة وتعديل المقررات"""

    class Meta:
        model = Course
        fields = ['name', 'code', 'department', 'description', 'credit_hours', 'image', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المقرر',
                'required': True
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رمز المقرر (مثل: CS101)',
                'required': True
            }),
            'department': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'وصف المقرر (اختياري)'
            }),
            'credit_hours': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'max': 6,
                'value': 3
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }
        labels = {
            'name': 'اسم المقرر',
            'code': 'رمز المقرر',
            'department': 'القسم',
            'description': 'وصف المقرر',
            'credit_hours': 'الساعات المعتمدة',
            'image': 'صورة المقرر',
            'is_active': 'نشط'
        }
        help_texts = {
            'name': 'أدخل اسماً واضحاً ومفهوماً للمقرر',
            'code': 'رمز فريد للمقرر (مثل: CS101, MATH201)',
            'description': 'وصف مختصر لمحتوى المقرر (اختياري)',
            'credit_hours': 'عدد الساعات المعتمدة للمقرر (1-6)',
            'image': 'صورة غلاف للمقرر (اختياري)',
            'department': 'اختر القسم الذي ينتمي إليه هذا المقرر'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # ترتيب الأقسام حسب الكلية والاسم
        self.fields['department'].queryset = Department.objects.filter(
            is_active=True
        ).select_related('faculty').order_by('faculty__name', 'name')

        # إضافة خيار فارغ
        self.fields['department'].empty_label = "اختر القسم"

        # جعل الوصف والصورة اختياريين
        self.fields['description'].required = False
        self.fields['image'].required = False

        # إضافة CSS classes إضافية
        for field_name, field in self.fields.items():
            if field.widget.attrs.get('class'):
                field.widget.attrs['class'] += ' mb-3'
            else:
                field.widget.attrs['class'] = 'mb-3'

    def clean_name(self):
        """التحقق من صحة اسم المقرر"""
        name = self.cleaned_data.get('name')

        if not name:
            raise ValidationError('اسم المقرر مطلوب.')

        if len(name.strip()) < 3:
            raise ValidationError('اسم المقرر يجب أن يكون 3 أحرف على الأقل.')

        if len(name) > 255:
            raise ValidationError('اسم المقرر طويل جداً (الحد الأقصى 255 حرف).')

        return name.strip()

    def clean_code(self):
        """التحقق من صحة رمز المقرر"""
        code = self.cleaned_data.get('code')

        if not code:
            raise ValidationError('رمز المقرر مطلوب.')

        code = code.strip().upper()

        if len(code) < 2:
            raise ValidationError('رمز المقرر يجب أن يكون حرفين على الأقل.')

        if len(code) > 20:
            raise ValidationError('رمز المقرر طويل جداً (الحد الأقصى 20 حرف).')

        # التحقق من عدم تكرار الرمز
        existing_course = Course.objects.filter(code=code)
        if self.instance and self.instance.pk:
            existing_course = existing_course.exclude(pk=self.instance.pk)

        if existing_course.exists():
            raise ValidationError('يوجد مقرر آخر بنفس الرمز.')

        return code

    def clean_description(self):
        """التحقق من صحة وصف المقرر"""
        description = self.cleaned_data.get('description')

        if description and len(description) > 2000:
            raise ValidationError('وصف المقرر طويل جداً (الحد الأقصى 2000 حرف).')

        return description.strip() if description else ''

    def clean_credit_hours(self):
        """التحقق من صحة الساعات المعتمدة"""
        credit_hours = self.cleaned_data.get('credit_hours')

        if credit_hours is None:
            raise ValidationError('الساعات المعتمدة مطلوبة.')

        if credit_hours < 1 or credit_hours > 6:
            raise ValidationError('الساعات المعتمدة يجب أن تكون بين 1 و 6.')

        return credit_hours

    def clean_image(self):
        """التحقق من صحة الصورة المرفوعة"""
        image = self.cleaned_data.get('image')

        if image:
            # التحقق من حجم الصورة (الحد الأقصى 5 ميجابايت)
            if image.size > 5 * 1024 * 1024:  # 5 MB
                raise ValidationError('حجم الصورة كبير جداً. الحد الأقصى المسموح 5 ميجابايت.')

            # التحقق من نوع الصورة
            allowed_image_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            if hasattr(image, 'content_type') and image.content_type not in allowed_image_types:
                raise ValidationError('نوع الصورة غير مدعوم. الأنواع المدعومة: JPEG, PNG, GIF, WebP.')

            # التحقق من امتداد الملف
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            file_name = image.name.lower()
            if not any(file_name.endswith(ext) for ext in allowed_extensions):
                raise ValidationError('امتداد الصورة غير مدعوم. الامتدادات المدعومة: jpg, jpeg, png, gif, webp.')

        return image

class CourseImageForm(forms.ModelForm):
    """نموذج مخصص لتحديث صورة المقرر فقط"""

    class Meta:
        model = Course
        fields = ['image']
        widgets = {
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            })
        }
        labels = {
            'image': 'صورة المقرر'
        }
        help_texts = {
            'image': 'اختر صورة جديدة للمقرر'
        }

    def clean_image(self):
        """التحقق من صحة الصورة المرفوعة"""
        image = self.cleaned_data.get('image')

        if image:
            # التحقق من حجم الصورة (الحد الأقصى 5 ميجابايت)
            if image.size > 5 * 1024 * 1024:  # 5 MB
                raise ValidationError('حجم الصورة كبير جداً. الحد الأقصى المسموح 5 ميجابايت.')

            # التحقق من نوع الصورة
            allowed_image_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            if hasattr(image, 'content_type') and image.content_type not in allowed_image_types:
                raise ValidationError('نوع الصورة غير مدعوم. الأنواع المدعومة: JPEG, PNG, GIF, WebP.')

            # التحقق من امتداد الملف
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            file_name = image.name.lower()
            if not any(file_name.endswith(ext) for ext in allowed_extensions):
                raise ValidationError('امتداد الصورة غير مدعوم. الامتدادات المدعومة: jpg, jpeg, png, gif, webp.')

        return image
