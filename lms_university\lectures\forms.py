from django import forms
from django.core.exceptions import ValidationError
from core.models import Course
from .models import Lecture

class LectureForm(forms.ModelForm):
    """نموذج إضافة وتعديل المحاضرات"""

    class Meta:
        model = Lecture
        fields = ['course', 'title', 'description', 'file', 'image']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'عنوان المحاضرة',
                'required': True
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'وصف المحاضرة (اختياري)'
            }),
            'course': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.ppt,.pptx,.txt,.zip,.rar'
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            })
        }
        labels = {
            'course': 'المقرر',
            'title': 'عنوان المحاضرة',
            'description': 'وصف المحاضرة',
            'file': 'ملف المحاضرة',
            'image': 'صورة المحاضرة'
        }
        help_texts = {
            'title': 'أدخل عنواناً واضحاً ومفهوماً للمحاضرة',
            'description': 'وصف مختصر لمحتوى المحاضرة (اختياري)',
            'file': 'يمكنك رفع ملفات PDF, Word, PowerPoint, أو ملفات مضغوطة',
            'image': 'صورة غلاف للمحاضرة (اختياري)',
            'course': 'اختر المقرر الذي تنتمي إليه هذه المحاضرة'
        }

    def __init__(self, *args, **kwargs):
        lecturer = kwargs.pop('lecturer', None)
        super().__init__(*args, **kwargs)

        # مؤقتاً: عرض جميع المقررات النشطة
        if lecturer:
            self.fields['course'].queryset = Course.objects.filter(
                is_active=True
            ).order_by('name')
        else:
            # إذا لم يتم تمرير محاضر، لا تظهر أي مقررات
            self.fields['course'].queryset = Course.objects.none()

        # إضافة خيار فارغ
        self.fields['course'].empty_label = "اختر المقرر"

        # جعل الوصف اختياري
        self.fields['description'].required = False

        # إضافة CSS classes إضافية
        for field_name, field in self.fields.items():
            if field.widget.attrs.get('class'):
                field.widget.attrs['class'] += ' mb-3'
            else:
                field.widget.attrs['class'] = 'mb-3'

    def clean_title(self):
        """التحقق من صحة عنوان المحاضرة"""
        title = self.cleaned_data.get('title')

        if not title:
            raise ValidationError('عنوان المحاضرة مطلوب.')

        if len(title.strip()) < 3:
            raise ValidationError('عنوان المحاضرة يجب أن يكون 3 أحرف على الأقل.')

        if len(title) > 255:
            raise ValidationError('عنوان المحاضرة طويل جداً (الحد الأقصى 255 حرف).')

        return title.strip()

    def clean_description(self):
        """التحقق من صحة وصف المحاضرة"""
        description = self.cleaned_data.get('description')

        if description and len(description) > 1000:
            raise ValidationError('وصف المحاضرة طويل جداً (الحد الأقصى 1000 حرف).')

        return description.strip() if description else ''

    def clean_file(self):
        """التحقق من صحة الملف المرفوع"""
        file = self.cleaned_data.get('file')

        if file:
            # التحقق من حجم الملف (الحد الأقصى 50 ميجابايت)
            if file.size > 50 * 1024 * 1024:  # 50 MB
                raise ValidationError('حجم الملف كبير جداً. الحد الأقصى المسموح 50 ميجابايت.')

            # التحقق من نوع الملف
            allowed_extensions = [
                '.pdf', '.doc', '.docx', '.ppt', '.pptx',
                '.txt', '.zip', '.rar', '.jpg', '.jpeg',
                '.png', '.gif', '.mp4', '.avi', '.mov'
            ]

            file_name = file.name.lower()
            if not any(file_name.endswith(ext) for ext in allowed_extensions):
                raise ValidationError(
                    'نوع الملف غير مدعوم. الأنواع المدعومة: PDF, Word, PowerPoint, '
                    'ملفات نصية, ملفات مضغوطة, صور, فيديو.'
                )

        return file

    def clean_image(self):
        """التحقق من صحة الصورة المرفوعة"""
        image = self.cleaned_data.get('image')

        if image:
            # التحقق من حجم الصورة (الحد الأقصى 5 ميجابايت)
            if image.size > 5 * 1024 * 1024:  # 5 MB
                raise ValidationError('حجم الصورة كبير جداً. الحد الأقصى المسموح 5 ميجابايت.')

            # التحقق من نوع الصورة
            allowed_image_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            if hasattr(image, 'content_type') and image.content_type not in allowed_image_types:
                raise ValidationError('نوع الصورة غير مدعوم. الأنواع المدعومة: JPEG, PNG, GIF, WebP.')

            # التحقق من امتداد الملف
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            file_name = image.name.lower()
            if not any(file_name.endswith(ext) for ext in allowed_extensions):
                raise ValidationError('امتداد الصورة غير مدعوم. الامتدادات المدعومة: jpg, jpeg, png, gif, webp.')

        return image

    def clean(self):
        """التحقق العام من النموذج"""
        cleaned_data = super().clean()
        course = cleaned_data.get('course')
        title = cleaned_data.get('title')

        # التحقق من عدم تكرار عنوان المحاضرة في نفس المقرر
        if course and title:
            existing_lecture = Lecture.objects.filter(
                course=course,
                title=title
            )

            # إذا كنا نعدل محاضرة موجودة، استثنيها من البحث
            if self.instance and self.instance.pk:
                existing_lecture = existing_lecture.exclude(pk=self.instance.pk)

            if existing_lecture.exists():
                raise ValidationError({
                    'title': 'يوجد محاضرة أخرى بنفس العنوان في هذا المقرر.'
                })

        return cleaned_data

class LectureSearchForm(forms.Form):
    """نموذج البحث في المحاضرات"""

    search = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'ابحث في المحاضرات...',
            'id': 'searchInput'
        }),
        label='البحث'
    )

    course = forms.ModelChoiceField(
        queryset=Course.objects.none(),
        required=False,
        empty_label="جميع المقررات",
        widget=forms.Select(attrs={
            'class': 'form-select',
            'id': 'courseFilter'
        }),
        label='المقرر'
    )

    def __init__(self, *args, **kwargs):
        lecturer = kwargs.pop('lecturer', None)
        super().__init__(*args, **kwargs)

        if lecturer:
            # مؤقتاً: الحصول على المقررات من خلال المحاضرات
            lecturer_course_ids = Lecture.objects.filter(lecturer=lecturer).values_list('course_id', flat=True).distinct()
            self.fields['course'].queryset = Course.objects.filter(
                id__in=lecturer_course_ids,
                is_active=True
            ).order_by('name')

class LectureImageForm(forms.ModelForm):
    """نموذج مخصص لتحديث صورة المحاضرة فقط"""

    class Meta:
        model = Lecture
        fields = ['image']
        widgets = {
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            })
        }
        labels = {
            'image': 'صورة المحاضرة'
        }
        help_texts = {
            'image': 'اختر صورة جديدة للمحاضرة'
        }

    def clean_image(self):
        """التحقق من صحة الصورة المرفوعة"""
        image = self.cleaned_data.get('image')

        if image:
            # التحقق من حجم الصورة (الحد الأقصى 5 ميجابايت)
            if image.size > 5 * 1024 * 1024:  # 5 MB
                raise ValidationError('حجم الصورة كبير جداً. الحد الأقصى المسموح 5 ميجابايت.')

            # التحقق من نوع الصورة
            allowed_image_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            if hasattr(image, 'content_type') and image.content_type not in allowed_image_types:
                raise ValidationError('نوع الصورة غير مدعوم. الأنواع المدعومة: JPEG, PNG, GIF, WebP.')

            # التحقق من امتداد الملف
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            file_name = image.name.lower()
            if not any(file_name.endswith(ext) for ext in allowed_extensions):
                raise ValidationError('امتداد الصورة غير مدعوم. الامتدادات المدعومة: jpg, jpeg, png, gif, webp.')

        return image
