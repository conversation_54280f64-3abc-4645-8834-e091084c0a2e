/* Global Font Settings */
* {
    font-family: 'Cairo', sans-serif !important;
}

html, body {
    height: 100%;
}

body {
    font-family: 'Cairo', sans-serif !important;
    background-color: #f8f9fa;
    font-weight: 400;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main {
    flex: 1;
}

/* Typography Improvements */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600;
}

.navbar-brand {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 700;
}

.btn {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500;
}

.form-control, .form-select, .form-check-label {
    font-family: 'Cairo', sans-serif !important;
}

.card-title {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600;
}

.nav-link {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500;
}

.dropdown-item {
    font-family: 'Cairo', sans-serif !important;
}

.alert {
    font-family: 'Cairo', sans-serif !important;
}

.badge {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500;
}

.table {
    font-family: 'Cairo', sans-serif !important;
}

.breadcrumb {
    font-family: 'Cairo', sans-serif !important;
}

.pagination {
    font-family: 'Cairo', sans-serif !important;
}

/* Input and Form Elements */
input, textarea, select {
    font-family: 'Cairo', sans-serif !important;
}

/* Labels and Text */
label, p, span, div {
    font-family: 'Cairo', sans-serif !important;
}

/* Links */
a {
    font-family: 'Cairo', sans-serif !important;
}

/* Navbar Gradient */
.navbar-gradient {
   background: linear-gradient(90deg, #17439c 0%, #3755e7 100%)!important;
   box-shadow: 0 2px 15px rgba(82, 96, 191, 0.3);
  border-bottom: none;
}

.navbar-brand {
    font-weight: bold;
    color: white !important;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: rgba(255, 255, 255, 0.9) !important;
    transform: scale(1.02);
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 0.25rem;
    padding: 0.5rem 1rem !important;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: white !important;
    background: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    overflow: hidden;
}

.dropdown-item {
    transition: all 0.3s ease;
    padding: 0.75rem 1.5rem;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #5260bf, #667eea);
    color: white;
    transform: translateX(5px);
}

.navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
.btn {
    border-radius: 8px;
}
.form-control {
    border-radius: 8px;
}
.alert {
    border-radius: 8px;
}
